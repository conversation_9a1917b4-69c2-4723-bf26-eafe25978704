const express = require('express');
const app = express();
const port = 3333;
const axios = require('axios');
const cors = require('cors');

app.use(express.json());
app.use(cors({ origin: '*' }));

app.use(
  express.static('public', {
    dotfiles: 'ignore',
    etag: false,
    extensions: ['htm', 'html'],
    index: false,
    maxAge: '1d',
    redirect: false,
    setHeaders: function (res, path, stat) {
      res.set('x-timestamp', Date.now());
    }
  })
);

app.post('/v4/personetics/execute', async (req, res) => {
  const { body } = req;
  const { authToken } = req.query;
  console.log('<<Proxy Hit>>');
  console.table({
    body
  });
  try {
    const { data } = await axios.post(
      `https://synovus-stg.personetics.com:15443/synovus-stg/execute?channel=SYNOVUS_QA`,
      body,
      {
        headers: {
          authToken: authToken ? authToken : 'B_1288',
          contentType: 'application/json',
          effectivetime: '01/03/2017 01:00'
        }
      }
    );
    return res.send(data);
  } catch (e) {
    console.log('PROXY ERROR', e);
    return res.send(e);
  }
});

app.listen(port, () => {
  console.log(`Example app listening on port ${port}`);
});
