import * as D3 from '../common/extension';

const createElement = (tag: string, className: string, text: string): HTMLElement => {
  const el = document.createElement(tag);
  el.className = className;
  el.textContent = text;
  return el;
};

const rightAlignAmount = () => {
  const accountDetails: any = document.getElementsByClassName('account-details');

  Array.from(accountDetails).forEach((element: any) => {
    const parent = element.parentElement;
    if (!parent || parent.querySelector('.synAccountType')) {
      return;
    }
    const [accountTypeText, accountBalanceText] = element.textContent?.split('$') ?? [];
    const fragment = document.createDocumentFragment();
    const accountType = createElement('h3', 'synAccountType', accountTypeText);
    const accountBalance = createElement('h3', 'synAccountBalance', `$${accountBalanceText}`);
    fragment.appendChild(accountType);
    fragment.appendChild(accountBalance);
    parent.appendChild(fragment);
    element.style.display = 'none';
  });
};

const extension: D3.Extension = {
  uiVersion: '6.2',
  eventName: 'amount-right-alignment',
  callOncePerPage: false,
  filter: () => {
    return document.querySelector('.account-preferences-container') != null;
  },
  isTargetPage: href => {
    return href.startsWith('preferences');
  },
  eventCallback: () => rightAlignAmount()
};

D3.init(extension);
