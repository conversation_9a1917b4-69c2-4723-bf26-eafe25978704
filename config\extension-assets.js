/*

  This file determines what extensions should be loaded.  It represents the call to d3rest/v3/extensions/assets.
  When running locally (npm start or yarn start) the WebPack Dev Server uses a proxy for all resources except
  this resource.

*/

require('./server-settings');

const path = fileName => {
  return `${process.env.extensionPath}${fileName}`;
};

module.exports = {
  extensions: [
    /** *************************************************
        Stylesheets
    ************************************************** */
    // {
    //   path: path('toggle-faq-answers.css'),
    //   assetType: 'STYLE',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('synovus-fonts.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('branding.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('branding-buttons.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // {
    //   path: path('branding-icons.css'),
    //   assetType: 'STYLE',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    // {
    // //   path: path('feature-call-out.css'),
    // //   //      loadOrder: 4,
    // //   assetType: 'STYLE',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },CTRL + K, C
    {
      path: path('disclosures.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('payment-success-modal.css'),
    // //   assetType: 'STYLE',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    // // {
    // //   path: path('digital-demos.css'),
    // //   assetType: 'STYLE',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('sso-de.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('sso-tlp.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // {
    //   path: path('overdraft-privileges.css'),
    //   assetType: 'STYLE',
    //   disabled: true,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('overdraft.css'),
      assetType: 'STYLE',
      disabled: true,
      assetVersion: '1.0.0',
      async: false
    },
    // {
    //   path: path('overdraft.css'),
    //   assetType: 'STYLE',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('survey.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('upcoming-transfers-adjustments.css'),
    // //   assetType: 'STYLE',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('open-account.css'),
      assetType: 'STYLE',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },

    /** *************************************************
        JavaScript Extensions
    ************************************************** */
    // {
    //   path: path('toggle-faq-answers.js'),
    //   assetType: 'SCRIPT',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('add-an-account.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('app-dynamics-eum.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('branding.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('content-injector.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('credentials.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    // // {
    // //   path: path('feature-call-out.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('disclosures.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('duplicate-name-fix.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('estatementPreferences.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('hide-sub-menu.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('mortagePortal.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('last-update-as-of.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    // {
    //   path: path('logout-link.js'),
    //   assetType: 'SCRIPT',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('survey.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('open-account.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('payment-success-modal.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('digital-demos.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('sso-de.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('sso-tlp.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // {
    //   path: path('overdraft-privileges.js'),
    //   assetType: 'SCRIPT',
    //   disabled: true,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('overdraft.js'),
      assetType: 'SCRIPT',
      disabled: true,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('wire.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // {
    //   path: path('overdraft.js'),
    //   assetType: 'SCRIPT',
    //   disabled: false,
    //   assetVersion: '1.0.0',
    //   async: false
    // },
    {
      path: path('survey.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    // // {
    // //   path: path('upcoming-transfers-adjustments.js'),
    // //   assetType: 'SCRIPT',
    // //   disabled: false,
    // //   assetVersion: '1.0.0',
    // //   async: false
    // // },
    {
      path: path('welcome-back-adjustments.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('synovus-fonts.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    },
    {
      path: path('amount-right-aligment.js'),
      assetType: 'SCRIPT',
      disabled: false,
      assetVersion: '1.0.0',
      async: false
    }
  ],
  fonts: []
};
