Feature: Teaser widget accounts/overview page

    Background:
        Given I am a user logged into the banking application
        And I am on the accounts overview page

    Scenario: User has enough transaction data to have insights
        When I have enough transaction history to have insights
        Then I can see the teaser widget under the "Pay & Transfer Tool" sidebar area
        And I can see a link to the Insights page under the teaser widget

    Scenario: User does not having enough data to generate insights
        When I do not have enough transaction history to create insights
        Then I do not see the teaser widget in the sidebar
        And instead I see a message letting me know I don't have insights yet

    <PERSON><PERSON><PERSON>: Waiting for insights to become available
        When the Personetics insights are not ready to display to the user
        Then then the teaser widget will not display any information
        And I see an empty teaser widget in the sidebar
        And I see a message saying that insights are being generated

    Scenario: Navigation from the teaser
        Given I want to see more information from a teaser item
        When I click the teaser
        Then I am taken to the insight page
        And that full story is displayed