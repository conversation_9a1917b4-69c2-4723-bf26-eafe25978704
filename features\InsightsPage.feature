Feature: Insights Page

  Background:
    Given I am a user logged into the banking application

  Scenario: Insight link is available in the top navigation
    Given I am on the accounts overview page
    Then I can see a link to the insights page

  Scenario: Unread insights, red dot notification display on navigation
    Given I am on the accounts overview page
    When I have unread insights
    Then I can see a red dot next to the Insight navigation link

  Scenario: New insights, red dot notification display on navigation
    Given I am on the accounts overview page
    When I have new insights available
    Then I can see a red dot next to the Insight navigation link

  Scenario: Navigating to the insights page
    Given I am on the accounts overview page
    When I click the Insights navigation item
    Then I am taken to the Insights page

  Scenario: Insights page
    Given I am have navigated to the insights page via main navigation
    Then I can see insight previews in the left sidebar
    And I see the first story opened from the sidebar

  Scenario: Navigation on the insights page
    Given I want to see the full story from a teaser
    When I click a story from the sidebar
    Then I can see the full story in the main layout of the insights page

