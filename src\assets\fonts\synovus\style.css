@font-face {
  font-family: 'Synovus-Icon-Font';
  src: url('Synovus-Icon-Font.eot');
  src:
    url('Synovus-Icon-Font.eot?#iefix') format('embedded-opentype'),
    url('Synovus-Icon-Font.woff') format('woff'),
    url('Synovus-Icon-Font.ttf') format('truetype'),
    url('Synovus-Icon-Font.svg#Synovus-Icon-Font') format('svg');
  font-weight: normal;
  font-style: normal;
}
[class^='icon-']:before {
  display: inline-block;
  font-family: 'Synovus-Icon-Font';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-logout:before {
  content: '\e900';
}
.icon-complete:before {
  content: '\0041';
}
.icon-help:before {
  content: '\0042';
}
.icon-alert:before {
  content: '\0043';
}
.icon-warning:before {
  content: '\0044';
}
.icon-search:before {
  content: '\0045';
}
.icon-delete:before {
  content: '\0046';
}
.icon-calendar:before {
  content: '\0047';
}
.icon-creditcard:before {
  content: '\0048';
}
.icon-download:before {
  content: '\0049';
}
.icon-email:before {
  content: '\004a';
}
.icon-edit:before {
  content: '\004b';
}
.icon-print:before {
  content: '\004c';
}
.icon-angle-right:before {
  content: '\004d';
}
.icon-close:before {
  content: '\004e';
}
.icon-money:before {
  content: '\004f';
}
.icon-add-icon:before {
  content: '\e901';
}
