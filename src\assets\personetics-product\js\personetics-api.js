function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

function _typeof(obj) {
  '@babel/helpers - typeof';
  if (typeof Symbol === 'function' && typeof Symbol.iterator === 'symbol') {
    _typeof = function _typeof(obj) {
      return typeof obj;
    };
  } else {
    _typeof = function _typeof(obj) {
      return obj &&
        typeof Symbol === 'function' &&
        obj.constructor === Symbol &&
        obj !== Symbol.prototype
        ? 'symbol'
        : typeof obj;
    };
  }
  return _typeof(obj);
}

/* eslint-disable */

/**
 * @preserve Personetics story API
 * API version: v2.0
 * Client version: 2021.story-client.5.0.0
 * Generated on: 10-19-21
 */
(function (window, Personetics, $, undefined) {
  var cfg = function cfg() {
    this.config = {
      //api
      protocolVersion: '2.6',
      enableNotifyEvents: true,
      eventDelayInterval: 2000,
      //processor
      useProjectDefaultCurrency: false,
      supportHtmlTagInProcessor: false,
      dateFormat: 'MM/DD/YY',
      amountFormat: '###,###,###',
      currencySymbol: '$',
      calendarSunFirst: true,
      //Api's contexId
      getInsights: 'sampleTopRelevant',
      getInboxInsights: 'dashboard',
      getInsightDetails: 'dashboard',
      getNumberOfInsights: 'sampleNew',
      getInsightRating: 'dashboard',
      sendEvents: 'dashboard',
      updateInsightRating: 'dashboard',
      updateInsightFeedback: 'dashboard',
      getBudgetsCategories: 'dashboard',
      setBudgetSettings: 'dashboard',
      defaultCtxId: 'dashboard',
      //reporting events
      analytics: false,
      //remote assets
      localBaseUrl: '../personetics-product/resources/assets',
      darkModeAsset: '/dark',
      defaultImageExtension: 'svg',
      storyBlocksConfigurations: {
        base: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        'account-selector': {
          defaultIconExtension: 'svg',
          defaultAssetName: 'account-default',
          defaultAssetDirectory: 'account_types'
        },
        'entity-list': {
          defaultIconExtension: 'svg',
          defaultAssetName: 'logo-default',
          defaultAssetDirectory: 'logos'
        },
        'entity-selector': {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        'entity-sub': {
          defaultIconExtension: 'svg',
          defaultAssetName: 'logo-default',
          defaultAssetDirectory: 'logos'
        },
        'feedback-transaction': {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        textBoxes: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        image: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'image-default',
          defaultAssetDirectory: 'story_images'
        },
        tranList: {
          displayInstallment: true
        }
      },
      teasersConfigurations: {
        general: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        base: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        },
        ManageBudget: {
          defaultIconExtension: 'svg',
          defaultAssetName: 'category-default',
          defaultAssetDirectory: 'category_icons'
        }
      },
      inboxAndStory: {
        autoTeaserSelect: true,
        showStoryTitle: true,
        containerINSTHeight: 496,
        inboxWidth: 325,
        storyWidth: 465
      },
      allowExternalTheme: true,
      // refresh inbox
      enableAutoRefresh: true,
      // auto display budget disclaimer modal
      autoDisclaimerModalDisplay: true,
      // Display budget modal title
      showBudgetModalTitle: true,
      // send presented event
      notifyEventCarouselPresented: true,
      notifyEventFeedPresented: true
    };
    this.agentMode = 'agent';
  };

  cfg.prototype.setConfig = function setConfig(config) {
    for (var configId in config) {
      if (config.hasOwnProperty(configId)) {
        this.config[configId] = config[configId];
      }
    }
  };

  cfg.prototype.getConfig = function (configId) {
    var path = typeof configId === 'string' ? configId.split('/') : configId;
    var configValue = null;
    var block = this;

    if (path.length) {
      $.each(path, function (ind, elm) {
        if (!configValue) {
          configValue = block.config[elm];
        } else {
          configValue = configValue[elm];
        }
      });
    } else {
      configValue = this.config[configId];
    }

    return configValue;
  };

  cfg.prototype.getConfigValues = function () {
    return this.config;
  };

  cfg.prototype.getApiCtxId = function (apiType, overrideCtxId) {
    if (overrideCtxId === this.agentMode) {
      return overrideCtxId;
    }

    var ctxId = overrideCtxId && overrideCtxId.length ? overrideCtxId : this.getConfig(apiType);
    ctxId = ctxId ? ctxId : this.getConfig('defaultCtxId');
    return ctxId;
  };

  Personetics.projectConfiguration = new cfg();
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoHelper = {};
  Personetics.utils.persoHelper.constants = {
    ANDROID_VERSIONS: {
      OLD_VERSION_MIN_TARGET: 4.4,
      OLD_VERSION_MAX_TARGET: 5.0
    }
  };

  Personetics.utils.persoHelper.getAndroidVersionNumber = function getAndroidVersionNumber() {
    var androidVersionNumber;
    var androidVersionString = getAndroidVersion();

    if (androidVersionString) {
      androidVersionNumber = parseFloat(getAndroidVersion());
    }

    return androidVersionNumber;
  };

  Personetics.utils.persoHelper.isOldAndroidVersion = function isOldAndriodVersion() {
    var androidVersion = this.getAndroidVersionNumber();
    var isOldAndroidVersion = false;

    if (typeof androidVersion !== 'undefined' && androidVersion > 0) {
      if (
        androidVersion < this.constants.ANDROID_VERSIONS.OLD_VERSION_MAX_TARGET &&
        androidVersion >= this.constants.ANDROID_VERSIONS.OLD_VERSION_MIN_TARGET
      ) {
        isOldAndroidVersion = true;
      }
    }

    return isOldAndroidVersion;
  };

  Personetics.utils.persoHelper.deepObjectExtend = function (out) {
    var self = this;
    out = out || {};

    for (var i = 1; i < arguments.length; i++) {
      var obj = arguments[i];
      if (!obj) continue;

      for (var key in obj) {
        if (obj.hasOwnProperty(key)) {
          if (_typeof(obj[key]) === 'object') {
            if (obj[key] instanceof Array == true) out[key] = obj[key].slice(0);
            else out[key] = self.deepObjectExtend(out[key], obj[key]);
          } else out[key] = obj[key];
        }
      }
    }

    return out;
  };

  var getAndroidVersion = function getAndroidVersion() {
    var userAgent = navigator.userAgent.toLowerCase();
    var match = userAgent.match(/android\s([0-9\.]*)/);
    return match ? match[1] : false;
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics) {
  var persoDict = function persoDict() {
    this.monthNames = {
      'en-short': [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec'
      ],
      en: [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
      ],
      he: [
        'ינואר',
        'פברואר',
        'מרץ',
        'אפריל',
        'מאי',
        'יוני',
        'יולי',
        'אוגוסט',
        'ספטמבר',
        'אוקטובר',
        'נובמבר',
        'דצמבר'
      ],
      'he-short': [
        "ינו'",
        "פבר'",
        'מרץ',
        "אפר'",
        'מאי',
        "יונ'",
        "יול'",
        "אוג'",
        "ספט'",
        "אוק'",
        "נוב'",
        "דצמ'"
      ],
      'es-short': [
        'Ene',
        'Feb',
        'Mar',
        'Abr',
        'May',
        'Jun',
        'Jul',
        'Ago',
        'Sep',
        'Oct',
        'Nov',
        'Dic'
      ],
      es: [
        'enero',
        'febrero',
        'marzo',
        'abril',
        'mayo',
        'junio',
        'julio',
        'agosto',
        'septiembre',
        'octubre',
        'noviembre',
        'diciembre'
      ],
      fr: [
        'Janvier',
        'Février',
        'Mars',
        'Avril',
        'Mai',
        'Juin',
        'Juillet',
        'Août',
        'Septembre',
        'Octobre',
        'Novembre',
        'Décembre'
      ],
      'fr-short': [
        'Jan',
        'Fév',
        'Mar',
        'Avr',
        'Mai',
        'Jun',
        'Jul',
        'Aoû',
        'Sep',
        'Oct',
        'Nov',
        'Déc'
      ],
      th: [
        'มกราคม',
        'กุมภาพันธ์',
        'มีนาคม',
        'เมษายน',
        'พฤษภาคม',
        'มิถุนายน',
        'กรกฎาคม',
        'สิงหาคม',
        'กันยายน',
        'ตุลาคม',
        'พฤศจิกายน',
        'ธันวาคม'
      ],
      'th-short': [
        'ม.ค.',
        'ก.พ.',
        'มี.ค.',
        'เม.ย.',
        'พ.ค.',
        'มิ.ย.',
        'ก.ค.',
        'ส.ค.',
        'ก.ย.',
        'ต.ค.',
        'พ.ย.',
        'ธ.ค.'
      ],
      de: [
        'Januar',
        'Februar',
        'März',
        'April',
        'Mai',
        'Juni',
        'Juli',
        'August',
        'September',
        'Oktober',
        'November',
        'Dezember'
      ],
      'de-short': [
        'Jan.',
        'Feb.',
        'März',
        'Apr.',
        'Mai',
        'Jun.',
        'Jul.',
        'Aug.',
        'Sept.',
        'Okt.',
        'Nov.',
        'Dez.'
      ],
      zh: [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
        '八月',
        '九月',
        '十月',
        '十一月',
        '十二月'
      ],
      'zh-short': [
        '一月',
        '二月',
        '三月',
        '四月',
        '五月',
        '六月',
        '七月',
        '八月',
        '九月',
        '十月',
        '十一月',
        '十二月'
      ],
      ko: ['1월', '2월', '3월', '4월', '5월', '6월', '7월', '8월', '9월', '10월', '11월', '12월'],
      'ko-short': [
        '1월',
        '2월',
        '3월',
        '4월',
        '5월',
        '6월',
        '7월',
        '8월',
        '9월',
        '10월',
        '11월',
        '12월'
      ],
      ja: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      'ja-short': [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
      ],
      it: [
        'gennaio',
        'febbraio',
        'marzo',
        'aprile',
        ' maggio',
        'giugno',
        'luglio',
        'agosto',
        'settembre',
        'ottobre',
        'novembre',
        'dicembre'
      ],
      'it-short': [
        'GEN',
        'FEB',
        'MAR',
        'APR',
        'MAG',
        'GIU',
        'LUG',
        'AGO',
        'SET',
        'OTT',
        'NOV',
        'DIC'
      ],
      pt: [
        'Janeiro',
        'Fevereiro',
        'Março',
        'Abril',
        'Maio',
        'Junho',
        'Julho',
        'Agosto',
        'Setembro',
        'Outubro',
        'Novembro',
        'Dezembro'
      ],
      'pt-short': [
        'Jan',
        'Fev',
        'Mar',
        'Abr',
        'Mai',
        'Jun',
        'Jul',
        'Ago',
        'Set',
        'Out',
        'Nov',
        'Dez'
      ]
    };
    this.weekDays = {
      en: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
      'en-short': ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
      'en-short-default': ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      he: ['ראשון', 'שני', 'שלישי', 'רביעי', 'חמישי', 'שישי', 'שבת'],
      es: ['Domingo', 'Lunes', 'Martes', 'Miércoles', 'Jueves', 'Viernes', 'Sábado'],
      de: ['Montag', 'Dienstag', 'Mittwoch', 'Donnerstag', 'Freitag', 'Samstag', 'Sonntag'],
      fr: ['dimanche', 'lundi', 'mardi', 'mercredi', 'jeudi', 'vendredi', 'samedi'],
      zh: ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'],
      'zh-short': ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      ko: ['일요일', '월요일', '화요일', '수요일', '목요일', '금요일', '토요일'],
      'ko-short': ['일', '월', '화', '수', '목', '금', '토'],
      ja: ['日曜日', '月曜日', '火曜日', '水曜日', '木曜日', '金曜日', '土曜日'],
      'ja-short': ['日', '月', '火', '水', '木', '金', '土'],
      it: ['lunedì', 'martedì', 'mercoledì', 'giovedì', 'venerdì', 'sabato', 'domenica'],
      'it-short': ['lun', 'mar', 'mer', 'gio', 'ven', 'sab', 'do'],
      pt: [
        'Segunda-feira',
        'Terça-feira',
        'Quarta-feira',
        'Quinta-feira',
        'Sexta-feira',
        'Sábado',
        'Domingo'
      ],
      'pt-short': ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sab', 'Dom']
    };
  };

  Personetics.pstoryDictionary = new persoDict();
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.accessibility = {};

  Personetics.utils.accessibility.isFocusable = function isFocusable(element) {
    //check if element is hidden from screen reader.
    if ($(element).is('[aria-hidden="true"], [aria-hidden="true"] *')) return false; //retrieve only text not nested in child tags.

    var hasText = $(element).contents().not($(element).children()).text().trim().length > 0;
    if ($(element).is(':input, [role], [aria-label], [aria-labelledby]') || hasText) return true;
    return false;
  };

  Personetics.utils.accessibility.firstChildFocusable = function firstChildFocusable(element) {
    if (typeof element == 'undefind' || element == null || element.length == 0) {
      return false;
    }

    var me = this;
    var elementChildren = element.find('*');
    var result = false;
    $.each(elementChildren, function (i, elem) {
      result = me.isFocusable(elem);

      if (result == true) {
        result = elem;
        return false;
      }
    });
    return result;
  };

  Personetics.utils.accessibility.formatAccessibleDate = function formatAccessibleDate(dateStr) {
    var lang = Personetics.processor.PStoryConfig.getConfig('lang');
    var formattedDate;

    if (lang === 'he' || lang === 'HE' || lang === 'He') {
      var day = Personetics.utils.date.dayName(dateStr, lang);
      var dayNumber = Personetics.utils.date.dayNumber(dateStr);
      var dateText = '';
      var month = Personetics.utils.date.monthName(dateStr, lang);
      var year = Personetics.utils.date.yearNumber(dateStr);
      dayNumber > 10 ? (dateText += 'ה' + day + ' ') : (dateText += day + ' ');
      dateText += 'ל' + month + ' ' + year;
      formattedDate = dateText;
    }

    return formattedDate;
  };

  Personetics.utils.accessibility.trackFocus = function trackFocus(element) {
    if (typeof element === 'undefined' || element.length === 0) {
      return;
    }

    var usingMouse;
    var el = element.get(0);

    var preFocus = function preFocus(event) {
      usingMouse = event.type === 'mousedown';
    };

    var addFocus = function addFocus(event) {
      if (usingMouse) {
        var className = event.target.getAttribute('class');
        event.target.setAttribute('class', className + ' personetics-mouse-focus');
      }
    };

    var removeFocus = function removeFocus(event) {
      var className = event.target.getAttribute('class');

      if (className !== null) {
        className = className.replace(/ personetics-mouse-focus/g, '');
        event.target.setAttribute('class', className);
      }
    };

    var bindEvents = function bindEvents() {
      el.removeEventListener('keydown', preFocus);
      el.removeEventListener('mousedown', preFocus);
      el.removeEventListener('focusin', addFocus);
      el.removeEventListener('focusout', removeFocus);
      el.addEventListener('keydown', preFocus);
      el.addEventListener('mousedown', preFocus);
      el.addEventListener('focusin', addFocus);
      el.addEventListener('focusout', removeFocus);
    };

    bindEvents();
  };

  Personetics.utils.accessibility.addAltTextToElement = function addAltTextToElement(
    element,
    altText
  ) {
    var accessibilityElementWrapper = $('<div class="perso-accessibility-wrapper"></div>');
    var accessibilityElement = $(
      '<span class="perso-accessibility-read"  role="text">' + altText + '</span>'
    );
    accessibilityElementWrapper.append(accessibilityElement);
    $(element).prepend(accessibilityElementWrapper);
    accessibilityElement.height($(element).outerHeight());
    accessibilityElement.width($(element).width());
  };

  Personetics.utils.accessibility.appendAltTextToElement = function appendAltTextToElement(
    element,
    altText
  ) {
    var accessibilityElementWrapper = $('<div class="perso-accessibility-wrapper"></div>');
    var accessibilityElement = $(
      '<span class="perso-accessibility-read"  role="text">' + altText + '</span>'
    );
    accessibilityElementWrapper.append(accessibilityElement);
    $(element).append(accessibilityElementWrapper);
    accessibilityElement.height(1);
    accessibilityElement.width(1);
  };

  Personetics.utils.accessibility.putFocus = function putFocus(elementForFocus) {
    var $element = $(elementForFocus);
    var focusInterval = 10; // ms, time between function calls

    var focusTotalRepetitions = 10; // number of repetitions

    this.putFocusAttr($element);
    $element.blur();
    var focusRepetitions = 0;
    var interval = window.setTimeout(function () {
      $element.focus();
    }, focusInterval);
  };

  Personetics.utils.accessibility.removeFocusAttr = function (elementForUnFocus) {
    var $element = $(elementForUnFocus);
    $element.attr({
      tabindex: '-1',
      'aria-hidden': true,
      role: 'none'
    });
    $element.blur();
  };

  Personetics.utils.accessibility.putFocusAttr = function (elementForUnFocus) {
    var $element = $(elementForUnFocus);
    $element.attr({
      tabindex: '0',
      'aria-hidden': false
    });
  };

  Personetics.utils.accessibility.getElementChildNodesText = function (el) {
    var str = '';

    function getText(el) {
      var $els = $(el).toArray();

      for (var i = 0; i < $els.length; i++) {
        var $el = $($els[i]);

        if ($el && $el.length) {
          if ($el.hasClass('perso-amount')) {
            var amount = $el.attr('aria-label') || $el.text();
            str += amount + ', ';
          } else if ($el.children().length) {
            getText($el.children());
          } else {
            var newStr =
              $el.text().length && !$el.hasClass('perso-accessibility-no-read')
                ? $el.text() + ', '
                : '';
            str += newStr;
          }
        }
      }
    }

    getText(el);
    return str;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.assert = {};

  Personetics.utils.assert.isUndefined = function isUndefined(obj) {
    return typeof obj === 'undefined';
  };

  Personetics.utils.assert.isNullOrUndefined = function isNullOrUndefined(obj) {
    return typeof obj === 'undefined' || obj == null;
  };

  Personetics.utils.assert.isTrue = function isTrue(obj) {
    var result = !Personetics.utils.assert.isUndefined(obj);
    return result && obj == true;
  };

  Personetics.utils.assert.isDefined = function isDefined(obj, nullable) {
    var result = !Personetics.utils.assert.isUndefined(obj);

    if (result && !Personetics.utils.assert.isUndefined(nullable) && nullable != null) {
      if (!nullable) result = obj != null;
    }

    return result;
  };

  Personetics.utils.assert.AssertIsDefined = function AssertIsDefined(obj, errorMessage) {
    if (Personetics.utils.assert.isNullOrUndefined(obj)) {
      if (errorMessage) Personetics.error(errorMessage, true);
      else Personetics.error('Object is undefined', true);
    }
  };

  Personetics.utils.assert.assignDefaultValue = function assignDefaultValue(
    variable,
    defaultValueToSet
  ) {
    var result = null;
    if (Personetics.utils.assert.isUndefined(variable)) result = defaultValueToSet;
    else result = variable;
    return result;
  };

  Personetics.utils.assert.isObject = function isObject(obj) {
    return _typeof(obj) === 'object';
  };

  Personetics.utils.assert.isString = function isString(obj) {
    return typeof obj === 'string';
  };

  Personetics.utils.assert.isArray = function isArray(obj) {
    return Object.prototype.toString.call(obj) === '[object Array]';
  };

  Personetics.utils.assert.isFunction = function isFunction(obj) {
    return typeof obj === 'function';
  };

  Personetics.utils.assert.exists = function (first, namespace) {
    var tokens = namespace.split('.');
    var obj = first;

    for (var i = 1; i < tokens.length; i++) {
      obj = obj == 'undefined' ? obj : obj[tokens[i]];
    }

    return obj;
  };

  Personetics.utils.assert.AssertIsNullOrUndefined = function (obj, message) {};
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $, undefined) {
  var d3 = window.d3;
  Personetics.utils = Personetics.utils || {};
  Personetics.utils.d3Extensions = {};
  var persoHelper = Personetics.utils.persoHelper;

  function appendElement(parentElm, children) {
    $.each(children, function (i, elm) {
      if (typeof elm.tagName == 'undefined') {
        var el = r2d3.select(parentElm).append('tspan');
        el.text($(elm).text());
        return;
      }

      var el = r2d3.select(parentElm).append(elm.nodeName.toLowerCase());
      var attrs = elm.attributes;
      $.each(attrs, function (j, attr) {
        el.attr(attr.name, attr.value);
      });

      if ($(elm).children().length > 0) {
        appendElement(el[0][0], $(elm).get(0).childNodes);
      } else {
        el.text($(elm).text());
      }
    });
  }

  function getBullet(list_class) {
    var asciCode = 32;

    switch (list_class) {
      case 'disc':
        asciCode = 9679;
        break;

      case 'square':
        asciCode = 9632;
        break;

      case 'circle':
        asciCode = 9675;
        break;

      case 'check-mark':
        asciCode = 10003;
        break;

      case 'arrow-right':
        asciCode = 10158;
        break;

      case 'none':
      default:
        asciCode = 32;
        break;
    }

    return '<tspan>' + String.fromCharCode(asciCode) + '</tspan>';
  }

  function renderLabelsText(value) {
    var d3Label = value;

    if (d3Label.indexOf('<ul') == 0) {
      var listBullet = $(d3Label).get(0).className.split(/\s+/)[1];
      var bullet = getBullet(listBullet);
      d3Label = d3Label.replace(/<ul/g, '<tspan');
      d3Label = d3Label.replace(/<\/ul>/g, '</tspan>');
      d3Label = d3Label.replace(/<li/g, '<tspan>' + bullet + '<tspan li="true"');
      d3Label = d3Label.replace(/<\/li>/g, '</tspan></tspan>');
    }

    d3Label = d3Label.replace(/<u/g, '<tspan text-decoration="underline"');
    d3Label = d3Label.replace(/<\/u>/g, '</tspan>');
    d3Label = d3Label.replace(/<b|<strong/g, '<tspan style="font-weight: bold"');
    d3Label = d3Label.replace(/<\/b>|<\/strong>/g, '</tspan>');
    d3Label = d3Label.replace(/<i|<em/g, '<tspan font-style="italic"');
    d3Label = d3Label.replace(/<\/i>|<\/em>/g, '</tspan>');
    d3Label = d3Label.replace(/<span|<div|<p/g, '<tspan');
    d3Label = d3Label.replace(/<\/(span|div|p)/g, '</tspan');
    d3Label = '<tspan>' + d3Label + '</tspan>';
    appendElement(this, $(d3Label));
  }

  Personetics.utils.d3Extensions.html = function (value) {
    return this.each(
      typeof value === 'function'
        ? function () {
            v = value.apply(this, arguments);
            if (typeof v != 'string') v = r2d3.select(v);
            renderLabelsText.call(this, v);
          }
        : function () {
            renderLabelsText.call(this, value);
          }
    ); // return this;
  };

  r2d3.selection.prototype.html = Personetics.utils.d3Extensions.html;

  Personetics.utils.d3Extensions.text = function text(value) {
    return arguments.length
      ? this.each(
          typeof value === 'function'
            ? function () {
                var v = value.apply(this, arguments);
                v = $(v).length > 0 ? $(v)[0].innerText : v;
                this.textContent = v == null ? '' : v;
              }
            : value == null
            ? function () {
                this.textContent = '';
              }
            : function () {
                this.textContent = $(value).length > 0 ? $(value)[0].innerText : value;
              }
        )
      : this.textContent;
  };

  Personetics.utils.d3Extensions.last = function () {
    var last = this.size() - 1;
    return r2d3.select(this[0][last]);
  };

  r2d3.selection.prototype.last = Personetics.utils.d3Extensions.last;

  if (typeof persoHelper !== 'undefined' && persoHelper.isOldAndroidVersion()) {
    // In old android versions override d3 "text" function with d3-extension "text" function.
    r2d3.selection.prototype.text = Personetics.utils.d3Extensions.text;
  }
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics) {
  var monthNumbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
  var monthNames = Personetics.pstoryDictionary.monthNames;
  var daysName = Personetics.pstoryDictionary.weekDays;
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.date = {};

  Personetics.utils.date.getStaticDate = function getStaticDate(dateStr) {
    var staticDateStr = dateStr.split('.')[0].replace(/T/g, ' ');
    staticDateStr = staticDateStr.split('-').join('/');
    return new Date(staticDateStr);
  };

  Personetics.utils.date.dayName = function dayName(dateStr, lang) {
    var dayNames = daysName[lang];
    var dayNumber = this.dayNumber(dateStr);
    var dayName;

    if (dayNumber <= 10) {
      dayName = dayNames[dayNumber - 1];
    } else {
      dayName = dayNumber;
    }

    return dayName;
  };

  Personetics.utils.date.monthName = function monthName(dateStr, lang, _short) {
    if (!(parseInt(dateStr) == dateStr)) {
      /**safari bug */
      var d = this.getStaticDate(dateStr);
      var month = d.getMonth();

      if (typeof month != 'undefined' && month != null && !isNaN(month)) {
        month = d.getMonth();
      }
    } else {
      try {
        var dn = parseInt(dateStr);

        if (dn >= 1 && dn <= 12) {
          month = dn - 1;
        } else {
          return dateStr;
        }
      } catch (e) {
        return dateStr;
      }
    }

    if (_short == true) lang = lang + '-short';
    var names = monthNames[lang];

    if (!names) {
      if (_short == true) names = monthNames['en-short'];
      else names = monthNames['en'];
    }

    return names[month];
  };

  Personetics.utils.date.dayNumber = function dayNumber(dateStr, dayFormat) {
    var parsedDateStr = dateStr.split('.')[0].replace(/T/g, ' ');
    parsedDateStr = parsedDateStr.split('-').join('/');
    dateStr = isNaN(Date.parse(parsedDateStr)) ? dateStr : parsedDateStr;
    var result = dateStr;
    var date = this.getStaticDate(dateStr);
    var day = date.getDate();
    if (!isNaN(day)) result = date.getDate();

    if (dayFormat && dayFormat == 'DD') {
      if (result < 10) result = '0' + result;
    }

    return result;
  };

  Personetics.utils.date.monthNumber = function monthNumber(dateStr) {
    if (!(parseInt(dateStr) == dateStr)) {
      /**safari bug */
      var d = this.getStaticDate(dateStr);
      var month = d.getMonth();

      if (typeof month != 'undefined' && month != null && !isNaN(month)) {
        month = d.getMonth();
      }
    } else {
      try {
        var dn = parseInt(dateStr);

        if (dn >= 1 && dn <= 12) {
          month = dn - 1;
        } else {
          return dateStr;
        }
      } catch (e) {
        return dateStr;
      }
    }

    var monthNum = monthNumbers[month];
    var result = monthNum;
    if (monthNum < 10) result = '0' + result;
    return result;
  };

  Personetics.utils.date.yearNumber = function yearNumber(dateStr, isShort) {
    var result = '';
    var d = this.getStaticDate(dateStr);
    var fullYear = d.getFullYear().toString();

    if (isShort) {
      result = fullYear.substring(2);
    } else result = fullYear;

    return result;
  };

  Personetics.utils.date.nthDate = function nthDate(dateStr, lang) {
    dateStr = dateStr.toLowerCase();
    var shortKey = lang + '-short';

    for (var i = 0; i < monthNames[shortKey].length; i++) {
      if (dateStr.indexOf(monthNames[shortKey][i].toLowerCase()) !== -1) {
        break;
      }
    }

    var dayNumber = dateStr.replace(monthNames[shortKey][i].toLowerCase(), '');
    var month = monthNames[lang][i];

    function nth(d) {
      if (d > 3 && d < 21) return 'th';

      switch (d % 10) {
        case 1:
          return 'st';

        case 2:
          return 'nd';

        case 3:
          return 'rd';

        default:
          return 'th';
      }
    }

    var isnum = /^\d+$/.test(dayNumber);

    if (!isnum) {
      return month;
    }

    dayNumber = parseInt(dayNumber, 10);
    var result = month + ' ' + dayNumber + nth(dayNumber);
    return result;
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.dictionary = {};
  /**
   * Overrides an object with key value pairs from another.
   * By default it overrides all keys (including null values). If you want to keep defined values, overrideDefinedVals should be false.
   * Returns a copy of original
   */

  Personetics.utils.dictionary.overrideObjectProperties = function (
    original,
    override,
    overrideDefinedVals
  ) {
    Personetics.utils.assert.assignDefaultValue(overrideDefinedVals, true);
    var originalCopy = $.extend({}, true, original);
    if (Personetics.utils.assert.isNullOrUndefined(override)) return originalCopy;
    if (Personetics.utils.assert.isNullOrUndefined(originalCopy)) return override;
    $.each(override, function (key, value) {
      if (!originalCopy.hasOwnProperty(key) || originalCopy[key] == null || overrideDefinedVals)
        originalCopy[key] = value;
    });
    return originalCopy;
  };

  Personetics.utils.dictionary.hasItem = function hasItem(obj, key, nullable) {
    var hasItem =
      Personetics.utils.assert.isObject(obj) &&
      Personetics.utils.string.isValidString(key) &&
      obj.hasOwnProperty(key);
    return nullable ? hasItem : hasItem && obj[key] != null;
  };

  Personetics.utils.dictionary.exists = function exists(obj) {
    var args = Array.prototype.slice.call(arguments, 1);

    for (var i = 0; i < args.length; i++) {
      if (!obj || !obj.hasOwnProperty(args[i])) {
        return false;
      }

      obj = obj[args[i]];
    }

    return true;
  };

  Personetics.utils.dictionary.removeInvalids = function removeInvalids(obj) {
    var result = {};

    if (typeof obj !== 'undefined' && obj) {
      $.each(obj, function (key, value) {
        if (value != null) {
          if (Personetics.utils.assert.isObject(value) && !Personetics.utils.assert.isArray(value))
            result[key] = Personetics.utils.dictionary.removeInvalids(value);
          else result[key] = value;
        }
      });
    }

    return result;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.emojisHelper = {};

  Personetics.utils.emojisHelper.parseEmoji = function parseEmoji(text) {
    var emojisCodes = extractEmojis(text);

    for (var i = 0; i < emojisCodes.length; i++) {
      var emojiSymbol = parseHexToEmoji(emojisCodes[i]);
      var emojiRegExp = new RegExp(emojisCodes[i], 'g');
      text = text.replace(emojiRegExp, '<span>' + emojiSymbol + '</span>');
    }

    text = text.replace(/Emoji/g, '').replace(/{{|}}/g, '');
    var parsedText = text;
    return parsedText;
  };
  /**
   * extractEmojis
   * Input: text - for example ("You may want to pay close attention {{Emoji 1F470 1F3FF}} to your spending this week.")
   * Output: text - emojis codes (1F470 1F3FF)
   * */

  var extractEmojis = function extractEmojis(str) {
    var emojisCodes = [];
    var emojiExp = extractEmojiExp(str);

    for (var i = 0; i < emojiExp.length; i++) {
      if (emojiExp[i].length) {
        var emojiCode = emojiExp[i];
        emojiCode = emojiCode.replace('Emoji', '');
        emojiCode = emojiCode.replace(/{{|}}/g, '');
        emojiCode = emojiCode.trim();

        if (emojisCodes.indexOf(emojiCode) === -1) {
          emojisCodes.push(emojiCode);
        }
      }
    }

    return emojisCodes;
  };
  /**
   * parseHexToEmoji
   * Input: emojis codes
   * Output: emoji symbol in hex (&#x1F470&#x1F3FF)
   * */

  var parseHexToEmoji = function parseHexToEmoji(emojisCodes) {
    var emojisCodesArr = emojisCodes.split(' ');
    var emoji = '';

    if (emojisCodesArr.length) {
      for (var i = 0; i < emojisCodesArr.length; i++) {
        emoji += '&#x' + emojisCodesArr[i];
      }
    }

    return emoji;
  };
  /**
   * Input: text
   * Output: text - for example"{{Emoji 1F470 1F3FF}}"*/

  var extractEmojiExp = function extractEmojiExp(str) {
    var emojiExpArr = [];
    var emojiExpOccurrence = (str.match(/{{Emoji/g) || []).length;
    var emojiOpenBrace = '{{Emoji';
    var emojiCloseBrace = '}}';

    for (var i = 0; i < emojiExpOccurrence; i++) {
      var expressionStartIndex = str.indexOf(emojiOpenBrace);
      var expressionEndIndex = str.indexOf(emojiCloseBrace) + emojiCloseBrace.length;
      var emojiExp = str.slice(expressionStartIndex, expressionEndIndex);
      str = str.replace(emojiExp, '');
      emojiExpArr.push(emojiExp);
    }

    return emojiExpArr;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.encodeDecode = {};
  Personetics.utils.encodeDecode.Base64 = {
    codex: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    encode: function encode(input) {
      var output = new Personetics.utils.string.StringBuffer();
      var enumerator = new Personetics.utils.encodeDecode.Utf8EncodeEnumerator(input);

      while (enumerator.moveNext()) {
        var chr1 = enumerator.current;
        enumerator.moveNext();
        var chr2 = enumerator.current;
        enumerator.moveNext();
        var chr3 = enumerator.current;
        var enc1 = chr1 >> 2;
        var enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
        var enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
        var enc4 = chr3 & 63;

        if (isNaN(chr2)) {
          enc3 = enc4 = 64;
        } else if (isNaN(chr3)) {
          enc4 = 64;
        }

        output.append(
          this.codex.charAt(enc1) +
            this.codex.charAt(enc2) +
            this.codex.charAt(enc3) +
            this.codex.charAt(enc4)
        );
      }

      return output.toString();
    },
    decode: function decode(input) {
      var output = new Personetics.utils.string.StringBuffer();
      var enumerator = new Personetics.utils.encodeDecode.Base64DecodeEnumerator(input);

      while (enumerator.moveNext()) {
        var charCode = enumerator.current;
        if (charCode < 128) output.append(String.fromCharCode(charCode));
        else if (charCode > 191 && charCode < 224) {
          enumerator.moveNext();
          var charCode2 = enumerator.current;
          output.append(String.fromCharCode(((charCode & 31) << 6) | (charCode2 & 63)));
        } else {
          enumerator.moveNext();
          var charCode2 = enumerator.current;
          enumerator.moveNext();
          var charCode3 = enumerator.current;
          output.append(
            String.fromCharCode(
              ((charCode & 15) << 12) | ((charCode2 & 63) << 6) | (charCode3 & 63)
            )
          );
        }
      }

      return output.toString();
    }
  };

  Personetics.utils.encodeDecode.Utf8EncodeEnumerator = function Utf8EncodeEnumerator(input) {
    this._input = input;
    this._index = -1;
    this._buffer = [];
    this.current = Number.NaN;

    this.moveNext = function () {
      if (this._buffer.length > 0) {
        this.current = this._buffer.shift();
        return true;
      } else if (this._index >= this._input.length - 1) {
        this.current = Number.NaN;
        return false;
      } else {
        var charCode = this._input.charCodeAt(++this._index); // "\r\n" -> "\n"
        //

        if (charCode == 13 && this._input.charCodeAt(this._index + 1) == 10) {
          charCode = 10;
          this._index += 2;
        }

        if (charCode < 128) {
          this.current = charCode;
        } else if (charCode > 127 && charCode < 2048) {
          this.current = (charCode >> 6) | 192;

          this._buffer.push((charCode & 63) | 128);
        } else {
          this.current = (charCode >> 12) | 224;

          this._buffer.push(((charCode >> 6) & 63) | 128);

          this._buffer.push((charCode & 63) | 128);
        }

        return true;
      }
    };
  };

  Personetics.utils.encodeDecode.Base64DecodeEnumerator = function Base64DecodeEnumerator(input) {
    this._input = input;
    this._index = -1;
    this._buffer = [];
    this.current = 64;

    this.moveNext = function () {
      if (this._buffer.length > 0) {
        this.current = this._buffer.shift();
        return true;
      } else if (this._index >= this._input.length - 1) {
        this.current = 64;
        return false;
      } else {
        var enc1 = Personetics.utils.encodeDecode.Base64.codex.indexOf(
          this._input.charAt(++this._index)
        );
        var enc2 = Personetics.utils.encodeDecode.Base64.codex.indexOf(
          this._input.charAt(++this._index)
        );
        var enc3 = Personetics.utils.encodeDecode.Base64.codex.indexOf(
          this._input.charAt(++this._index)
        );
        var enc4 = Personetics.utils.encodeDecode.Base64.codex.indexOf(
          this._input.charAt(++this._index)
        );
        var chr1 = (enc1 << 2) | (enc2 >> 4);
        var chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
        var chr3 = ((enc3 & 3) << 6) | enc4;
        this.current = chr1;
        if (enc3 != 64) this._buffer.push(chr2);
        if (enc4 != 64) this._buffer.push(chr3);
        return true;
      }
    };
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  var Handlebars = Personetics.UI.Handlebars;
  Handlebars.registerHelper({
    /**
     * Loop of the internal template in the 'For' structure
     * @param {Number} from Number to start
     * @param {Number} to Number to end
     * @param {Number} incr Number for skips
     * @param {String} paramName The name of the value
     * @param {*} block
     */
    for: function _for(from, to, incr, paramName, block) {
      var accum = '',
        obj;

      for (var i = from; i < to; i += incr) {
        obj = {};
        obj[paramName] = i;
        accum += block.fn(obj);
      }

      return accum;
    },

    /**
     * Get an array item in a specific index
     * @param {Number} index
     * @param {Array} array
     */
    getItemInArray: function getItemInArray(index, array) {
      return array && array[index];
    },

    /**
     * Calculate the values with the operator
     * @param {*} lvalue One value
     * @param {String} operator The operator
     * @param {*} rvalue Two value
     */
    math: function math(lvalue, operator, rvalue) {
      return {
        '+': lvalue + rvalue,
        '-': lvalue - rvalue,
        '*': lvalue * rvalue,
        '/': lvalue / rvalue,
        '%': lvalue % rvalue,
        '==': lvalue == rvalue,
        '||': lvalue || rvalue
      }[operator];
    },

    /**
     * return lower case string
     * @param {String}
     */
    toLowerCase: function toLowerCase(str) {
      if (str && str !== 'undefined') {
        return str.toLowerCase();
      }
    }
  });
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.json = {};
  var JSON = window.JSON || {};
  window.JSON = JSON;

  JSON.stringify =
    JSON.stringify ||
    function (obj) {
      var t = _typeof(obj);

      if (t != 'object' || obj === null) {
        // simple data type
        if (t == 'string') obj = '"' + obj + '"';
        return String(obj);
      } else {
        // recurse array or object
        var n,
          v,
          json = [],
          arr = obj && obj.constructor == Array;

        for (n in obj) {
          if (obj.hasOwnProperty(n)) {
            v = obj[n];
            t = _typeof(v);
            if (t == 'string') v = '"' + v + '"';
            else if (t == 'object' && v !== null) v = JSON.stringify(v);
            json.push((arr ? '' : '"' + n + '":') + String(v));
          }
        }

        return (arr ? '[' : '{') + String(json) + (arr ? ']' : '}');
      }
    };

  Personetics.utils.json.getJsonStringifyMethod = function getJsonStringifyMethod() {
    if ($.toJSON) {
      return $.toJSON;
    } else {
      return JSON.stringify;
    }
  };

  Personetics.utils.json.toJSON = function toJSON(value) {
    var _json_stringify = Personetics.utils.json.getJsonStringifyMethod();

    if (
      typeof Prototype !== 'undefined' &&
      /*parseFloat(Prototype.Version.substr(0,3)) < 1.7 &&*/
      typeof Array.prototype.toJSON !== 'undefined'
    ) {
      var _array_tojson = Array.prototype.toJSON;
      delete Array.prototype.toJSON;

      var r = _json_stringify(value);

      Array.prototype.toJSON = _array_tojson;
      return r;
    } else return _json_stringify(value);
  };

  Personetics.utils.json.replacer = function replacer(match, pIndent, pKey, pVal, pEnd) {
    var key = '<span class=json-key>';
    var val = '<span class=json-value>';
    var str = '<span class=json-string>';
    var r = pIndent || '';
    if (pKey) r = r + key + pKey.replace(/[": ]/g, '') + '</span>: ';
    if (pVal) r = r + (pVal[0] == '"' ? str : val) + pVal + '</span>';
    return r + (pEnd || '');
  };

  Personetics.utils.json.prettyPrint = function prettyPrint(obj) {
    var jsonLine = /^( *)("[\w.+-]+": )?("[^"]*"|[\w.+-]*)?([,[{])?$/gm;
    return JSON.stringify(obj, null, 3)
      .replace(/&/g, '&amp;')
      .replace(/\\"/g, '&quot;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(jsonLine, Personetics.utils.json.replacer);
  };

  Personetics.utils.json.mergeString = function merge(firstjsonString, secondJsonString) {
    var json1 = JSON.parse(firstjsonString);
    var json2 = JSON.parse(secondJsonString);
    var result = Personetics.utils.json.mergeObject(json1, json2);
    var objectString = Personetics.utils.json.toJSON(result);
    return objectString;
  };

  Personetics.utils.json.mergeObject = function merge(firstjson, secondJson) {
    var mergedJson = $.extend({}, firstjson, secondJson);
    return mergedJson;
  };

  Personetics.utils.json.mergeDeepObject = function merge(firstjson, secondJson) {
    var mergedJson = $.extend(true, {}, firstjson, secondJson);
    return mergedJson;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils || {};
  Personetics.PLoggerModes = {
    VERBOSE: {
      name: 'VERBOSE',
      value: 1
    },
    LOG: {
      name: 'LOG',
      value: 2
    },
    DEBUG: {
      name: 'DEBUG',
      value: 3
    },
    ERROR: {
      name: 'ERROR',
      value: 4
    },
    PRODUCTION: {
      name: 'PRODUCTION',
      value: 5
    }
  };

  var logger = function logger() {
    this.mode = Personetics.PLoggerModes.ERROR.value;

    if (typeof jsServerContextObj !== 'undefined') {
      this.mode = jsServerContextObj.getLogLevel();
    }
  };

  logger.prototype.setDebugMode = function setDebugMode(mode) {
    if (
      typeof mode !== 'undefined' &&
      mode !== null &&
      mode.hasOwnProperty('name') &&
      Personetics.PLoggerModes.hasOwnProperty(mode.name)
    )
      this.mode = mode.value;
  };

  logger.prototype.log = function log(msg) {
    if (this.mode <= Personetics.PLoggerModes.LOG.value) console.log(msg);
  };

  logger.prototype.verbose = function verbose(msg) {
    if (this.mode <= Personetics.PLoggerModes.VERBOSE.value) console.log(msg);
  };

  logger.prototype.debug = function debug(msg) {
    if (this.mode <= Personetics.PLoggerModes.DEBUG.value) console.log(msg);
  };

  logger.prototype.error = function (msg, throwException, errorObject, options) {
    if (this.mode <= Personetics.PLoggerModes.ERROR.value) {
      var shouldDisplayError =
        typeof options !== 'undefined' &&
        options.hasOwnProperty('shouldDisplayError') &&
        typeof options.shouldDisplayError !== 'undefined'
          ? options.shouldDisplayError
          : true;

      if (shouldDisplayError) {
        var context = Personetics.processor.PStoryConfig.getConfig('context');
        var isPermutations = Personetics.processor.PStoryConfig.getConfig('isPermutations');

        if (isPermutations) {
          console.log('<< Permutation Not Available >>: ' + msg);
        } else if (context === 'server') {
          throw msg;
        } else if (personetics && personetics.config && personetics.config.persoEBMode === true) {
          if (errorObject && typeof errorObject !== 'undefined') {
            errorObject.msg = msg;
            throw errorObject;
          }

          throw msg;
        } else if (throwException) {
          console.error('<< ERROR >>: ' + msg);
        }
      }

      if (Personetics.utils.assert.isDefined(errorObject, false)) throw errorObject;
    }
  };

  Personetics.utils.PLogger = new logger(); // Do not use this function, it's only for compatibility of older versions.

  Personetics.verbose = Personetics.utils.PLogger.verbose.bind(Personetics.utils.PLogger); // Do not use this function, it's only for compatibility of older versions.

  Personetics.debug = Personetics.utils.PLogger.debug.bind(Personetics.utils.PLogger);
  Personetics.log = Personetics.utils.PLogger.log.bind(Personetics.utils.PLogger);
  Personetics.error = Personetics.utils.PLogger.error.bind(Personetics.utils.PLogger);
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.messages = {};

  Personetics.utils.messages.errorMessage = function errorMessage(params) {
    var errorMsgTemplate =
      Personetics.UI.Handlebars.templates[
        params ? (params.templateId ? params.templateId : 'error-msg') : 'error-msg'
      ];

    if (
      typeof personetics !== 'undefined' &&
      personetics !== null &&
      personetics.hasOwnProperty('getDeviceType')
    ) {
      var deviceType = personetics.getDeviceType() || 'web';
    }

    var html;

    if (deviceType === 'web') {
      html = errorMsgTemplate({
        textLarge: params.textLarge,
        textSmall: params.textSmall,
        imgClass: params.imgClass,
        btnText: params.btnText
      });
    } else {
      html = errorMsgTemplate({
        textLarge: params.textLarge,
        textSmall: params.textSmall,
        imgClass: params.imgClass,
        btnText: params.btnText,
        isMobileDevice: true //"perso-mobile-err"
      });
    }

    return html;
  };

  Personetics.utils.messages.noInsightsMessage = function noInsightsMessage(params) {
    var errorMsgTemplate =
      Personetics.UI.Handlebars.templates[
        params
          ? params.templateId
            ? params.templateId
            : 'no-insights-message'
          : 'no-insights-message'
      ];

    if (
      typeof personetics !== 'undefined' &&
      personetics !== null &&
      personetics.hasOwnProperty('getDeviceType')
    ) {
      var deviceType = personetics.getDeviceType() || 'web';
    }

    var html;

    if (deviceType === 'web') {
      html = errorMsgTemplate({
        textLarge: params.textLarge,
        textSmall: params.textSmall,
        imgClass: params.imgClass
      });
    } else {
      html = errorMsgTemplate({
        textLarge: params.textLarge,
        textSmall: params.textSmall,
        imgClass: params.imgClass,
        isMobileDevice: true //"mobile-no-insights"
      });
    }

    return html;
  };

  Personetics.utils.messages.noFilteredInsightsMessage = function noFilteredInsightsMessage(
    params
  ) {
    var noFileredMsgTemplate =
      Personetics.UI.Handlebars.templates[
        params
          ? params.templateId
            ? params.templateId
            : 'no-filtered-insights-msg'
          : 'no-filtered-insights-msg'
      ];

    if (
      typeof personetics !== 'undefined' &&
      personetics !== null &&
      personetics.hasOwnProperty('getDeviceType')
    ) {
      var deviceType = personetics.getDeviceType() || 'web';
    }

    var html;

    if (deviceType === 'web') {
      html = noFileredMsgTemplate({
        text: params.text
      });
    } else {
      html = noFileredMsgTemplate({
        text: params.text,
        isMobileDevice: true //"mobile-no-insights"
      });
    }

    return html;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.network = {};
  Personetics.utils.network.consts = {
    base64EncodeResponse: false,
    requestTimeoutInMillis: 300000,
    dataType: 'json',
    async: true,
    onComplete: function onComplete() {},
    onSuccess: function onSuccess(data, textStatus, jqXHR) {
      Personetics.log('request success');
    },
    onFailure: function onFailure(jqXHR, textStatus, errorThrown) {
      Personetics.log('request failed: ' + textStatus + ', ' + errorThrown);
    }
  };

  Personetics.utils.network.doAjaxGet = function doAjaxGet(url, data, requestOptions) {
    var finalRequestOptions = Personetics.utils.dictionary.overrideObjectProperties(
      requestOptions,
      Personetics.utils.network.consts,
      false
    );
    Personetics.utils.assert.AssertIsDefined(
      url,
      'Personetics.utils.network.doAjaxGet: invalid request URL: ' + url
    );
    var config = {
      type: 'GET',
      url: url,
      dataType: finalRequestOptions.dataType,
      async: finalRequestOptions.async,
      timeout: finalRequestOptions.requestTimeoutInMillis,
      complete: finalRequestOptions.onComplete,
      success: onDoAjaxGetSuccess,
      error: onDoAjaxGetError
    };
    if (!Personetics.utils.assert.isNullOrUndefined(data)) config.data = data;
    $.ajax(config);

    function onDoAjaxGetSuccess(response) {
      var processedResponse = response;

      if (finalRequestOptions.base64EncodeResponse == true) {
        processedResponse = Personetics.utils.network.decodeDataWithBase64(response);
        processedResponse = $.parseJSON(processedResponse);
      }

      finalRequestOptions.onSuccess(processedResponse);
    }

    function onDoAjaxGetError(XMLHttpRequest, textStatus, errorThrown) {
      finalRequestOptions.onFailure(XMLHttpRequest, textStatus, errorThrown);
    }
  };

  Personetics.utils.network.doAjaxPost = function doAjaxPost(url, postData, requestOptions) {
    var finalRequestOptions = Personetics.utils.dictionary.overrideObjectProperties(
      requestOptions,
      Personetics.utils.network.consts,
      false
    );
    Personetics.utils.assert.AssertIsNullOrUndefined(
      url,
      'Personetics.utils.network.doAjaxPost: invalid request URL: ' + url
    );
    $.ajax({
      type: 'POST',
      url: url,
      data: postData,
      dataType: finalRequestOptions.dataType,
      async: finalRequestOptions.async,
      headers: finalRequestOptions.headers,
      timeout: finalRequestOptions.requestTimeoutInMillis,
      complete: finalRequestOptions.onComplete,
      success: onDoAjaxPostSuccess,
      error: onDoAjaxPostError
    });

    function onDoAjaxPostSuccess(response) {
      var processedResponse = response;

      if (finalRequestOptions.base64EncodeResponse == true) {
        processedResponse = Personetics.utils.network.decodeDataWithBase64(response);
        processedResponse = $.parseJSON(processedResponse);
      }

      finalRequestOptions.onSuccess(processedResponse);
    }

    function onDoAjaxPostError(XMLHttpRequest, textStatus, errorThrown) {
      finalRequestOptions.onFailure(XMLHttpRequest, textStatus, errorThrown);
    }
  };

  Personetics.utils.network.queueAjaxPost = function queueAjaxPost(
    queueId,
    url,
    postData,
    requestOptions
  ) {
    var finalRequestOptions = Personetics.utils.dictionary.overrideObjectProperties(
      requestOptions,
      Personetics.utils.network.consts,
      false
    );
    return $.ajaxq(queueId, {
      type: 'POST',
      url: url,
      data: postData,
      dataType: finalRequestOptions.dataType,
      timeout: finalRequestOptions.requestTimeoutInMillis,
      success: onDoAjaxQueuePostSuccess,
      error: onDoAjaxQueuePostError
    });

    function onDoAjaxQueuePostSuccess(response) {
      var processedResponse = response;

      if (finalRequestOptions.base64EncodeResponse == true) {
        processedResponse = Personetics.utils.network.decodeDataWithBase64(response);
        processedResponse = $.parseJSON(processedResponse);
      }

      finalRequestOptions.onSuccess(processedResponse);
    }

    function onDoAjaxQueuePostError(XMLHttpRequest, textStatus, errorThrown) {
      finalRequestOptions.onFailure(XMLHttpRequest, textStatus, errorThrown);
    }
  };

  Personetics.utils.network.removeAjaxPostQueue = function (queueId) {
    $.ajaxq(queueId);
  };

  Personetics.utils.network.wasUserAborted = function wasUserAborted(xhr, textStatus) {
    return !xhr.getAllResponseHeaders() && textStatus != 'timeout';
  };

  Personetics.utils.network.createPostform = function createPostform(
    params,
    absoluteUrl,
    newDoc,
    win,
    pageInfo
  ) {
    newDoc.charset = 'utf-8';
    var formId = 'hintForm';
    var form = newDoc.createElement('form');
    form.setAttribute('id', formId);
    form.setAttribute('method', 'post');
    form.setAttribute('action', absoluteUrl); // form.acceptCharset='utf-8'

    for (var i in params) {
      if (params.hasOwnProperty(i)) {
        var input = newDoc.createElement('input');
        input.type = 'hidden';
        input.name = i;
        input.value = params[i];
        form.appendChild(input);
      }
    }

    newDoc.body.appendChild(form);
    form.submit();
    newDoc.body.removeChild(form);
  };

  Personetics.utils.network.decodeDataWithBase64 = function (data) {
    data = data.replace(/(\r\n|\n|\r)/gm, '');
    data = data.replace(/"/g, ''); //  ""example"" ---> "example"

    var decodedData = Personetics.utils.encodeDecode.Base64.decode(data);
    return decodedData;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoBrowserDetector = {};
  var scope;

  Personetics.utils.persoBrowserDetector.init = function () {
    scope = this;
    this.browser = searchString(dataBrowser) || 'An unknown browser';
    this.version =
      searchVersion(navigator.userAgent) ||
      searchVersion(navigator.appVersion) ||
      'an unknown version';
    this.OS = searchString(dataOS) || 'an unknown OS';
    this.isMSIE = detectIE() || false;
  };

  var searchString = function searchString(data) {
    for (var i = 0; i < data.length; i++) {
      var dataString = data[i].string;
      var dataProp = data[i].prop;
      scope.versionSearchString = data[i].versionSearch || data[i].identity;

      if (dataString) {
        if (dataString.indexOf(data[i].subString) != -1) {
          return data[i].identity;
        }
      } else if (dataProp) {
        return data[i].identity;
      }
    }
  };

  var searchVersion = function searchVersion(dataString) {
    var index = dataString.indexOf(scope.versionSearchString);
    if (index == -1) return;
    return parseFloat(dataString.substring(index + scope.versionSearchString.length + 1));
  };

  var detectIE = function detectIE() {
    var ua = navigator.userAgent; // Test values; Uncomment to check result …
    // IE 10
    // ua = 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.2; Trident/6.0)';
    // IE 11
    // ua = 'Mozilla/5.0 (Windows NT 6.3; Trident/7.0; rv:11.0) like Gecko';
    // Edge 12 (Spartan)
    // ua = 'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/39.0.2171.71 Safari/537.36 Edge/12.0';
    // Edge 13
    // ua = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/46.0.2486.0 Safari/537.36 Edge/13.10586';

    var msie = ua.indexOf('MSIE ');

    if (msie > 0) {
      // IE 10 or older => return version number
      return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
    }

    var trident = ua.indexOf('Trident/');

    if (trident > 0) {
      // IE 11 => return version number
      var rv = ua.indexOf('rv:');
      return parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
    }

    var edge = ua.indexOf('Edge/');

    if (edge > 0) {
      // Edge (IE 12+) => return version number
      return parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
    } // other browser

    return false;
  };

  Personetics.utils.persoBrowserDetector.getBrowserData = function () {
    return {
      browser: this.browser,
      version: this.version,
      os: this.OS,
      deviceType: getDeviceType(),
      deviceSystem: getDeviceSystem(),
      isMSIE: this.isMSIE
    };
  };

  var dataBrowser = [
    {
      string: navigator.userAgent,
      subString: 'Chrome',
      identity: 'Chrome',
      versionSearch: 'Chrome'
    },
    {
      string: navigator.userAgent,
      subString: 'OmniWeb',
      versionSearch: 'OmniWeb/',
      identity: 'OmniWeb'
    },
    {
      string: navigator.vendor,
      subString: 'Apple',
      identity: 'Safari',
      versionSearch: 'Version'
    },
    {
      prop: window.opera,
      identity: 'Opera',
      versionSearch: 'Version'
    },
    {
      string: navigator.vendor,
      subString: 'iCab',
      identity: 'iCab',
      versionSearch: 'iCab'
    },
    {
      string: navigator.vendor,
      subString: 'KDE',
      identity: 'Konqueror',
      versionSearch: 'Konqueror'
    },
    {
      string: navigator.userAgent,
      subString: 'Firefox',
      identity: 'Firefox',
      versionSearch: 'Firefox'
    },
    {
      string: navigator.vendor,
      subString: 'Camino',
      identity: 'Camino',
      versionSearch: 'Camino'
    },
    {
      // for newer Netscapes (6+)
      string: navigator.userAgent,
      subString: 'Netscape',
      identity: 'Netscape',
      versionSearch: 'Netscape'
    },
    {
      string: navigator.userAgent,
      subString: 'MSIE',
      identity: 'Explorer',
      versionSearch: 'MSIE'
    },
    {
      string: navigator.userAgent,
      subString: 'Gecko',
      identity: 'Mozilla',
      versionSearch: 'rv'
    },
    {
      string: navigator.userAgent,
      subString: 'Mozilla',
      identity: 'Mozilla',
      versionSearch: 'Mozilla'
    },
    {
      // for older Netscapes (4-)
      string: navigator.userAgent,
      subString: 'Mozilla',
      identity: 'Netscape',
      versionSearch: 'Mozilla'
    }
  ];
  var dataOS = [
    {
      string: navigator.platform,
      subString: 'Win',
      identity: 'Windows',
      versionSearch: 'Windows'
    },
    {
      string: navigator.platform,
      subString: 'Mac',
      identity: 'Mac',
      versionSearch: 'Mac'
    },
    {
      string: navigator.userAgent,
      subString: 'iPhone',
      identity: 'iPhone/iPod/iPad',
      versionSearch: 'iPhone/iPod/iPad/rv'
    },
    {
      string: navigator.platform,
      subString: 'Linux',
      identity: 'Linux',
      versionSearch: 'Linux'
    }
  ];
  var isMobile = {
    Android: function Android() {
      return navigator.userAgent.match(/Android/i);
    },
    BlackBerry: function BlackBerry() {
      return navigator.userAgent.match(/BlackBerry/i);
    },
    iOS: function iOS() {
      return navigator.userAgent.match(/iPhone|iPod/i);
    },
    Opera: function Opera() {
      return navigator.userAgent.match(/Opera Mini/i);
    },
    Windows: function Windows() {
      return navigator.userAgent.match(/IEMobile/i);
    },
    any: function any() {
      return (
        isMobile.Android() ||
        isMobile.BlackBerry() ||
        isMobile.iOS() ||
        isMobile.Opera() ||
        isMobile.Windows()
      );
    }
  };
  var isTablet = {
    Android: function Android() {
      return navigator.userAgent.match(/Tablet|Kindle/i);
    },
    iOS: function iOS() {
      return (
        navigator.userAgent.match(/iPad/i) ||
        (navigator.userAgent.match(/Mac/) &&
          navigator.maxTouchPoints &&
          navigator.maxTouchPoints > 1)
      );
    },
    any: function any() {
      return isTablet.Android() || isTablet.iOS();
    }
  };
  var deviceSystem = {
    Android: function Android() {
      return navigator.userAgent.match(/Android|Tablet/i) && 'android';
    },
    iOS: function iOS() {
      return (
        (navigator.userAgent.match(/iPhone|iPod|iPad/i) ||
          (navigator.userAgent.match(/Mac/) &&
            navigator.maxTouchPoints &&
            navigator.maxTouchPoints > 1)) &&
        'ios'
      );
    },
    any: function any() {
      return deviceSystem.Android() || deviceSystem.iOS();
    }
  }; //Used in case the device type is different from the browser device type

  var deviceType = null;

  Personetics.utils.persoBrowserDetector.setDeviceType = function setDeviceType(type) {
    deviceType = type;
  };

  function getDeviceSystem() {
    return deviceSystem.any();
  }

  function getDeviceType() {
    if (deviceType) return deviceType;
    return isMobile.any() ? 'perso-mobile' : isTablet.any() ? 'perso-tablet' : 'perso-desktop';
  }

  Personetics.utils.persoBrowserDetector.init();
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoInboxReporter = {};

  Personetics.utils.persoInboxReporter.init = function (all, teaserView, unread, filterTab) {
    this.config = {
      filterTab: filterTab || 'all',
      teaserView: teaserView || null,
      presentedEventType: 'Presented',
      presented: 'presented',
      value: '1',
      $teaserElement: '.perso-teaser-template',
      $persoTeaserTemplate: '#perso-teaser-template_'
    };
    this.insights = {
      all: this.convertInsightArrayToObject(all),
      unread: this.convertInsightArrayToObject(unread)
    };
    this.insightElms = {};
    this.initialized = true;
  };

  Personetics.utils.persoInboxReporter.updateInboxFilterTab = function (filterTab) {
    if (this.initialized) {
      this.config.filterTab = filterTab;
    }
  };

  Personetics.utils.persoInboxReporter.isInboxReporterInitialized = function () {
    return this.initialized;
  };

  Personetics.utils.persoInboxReporter.getInboxReporterInsights = function () {
    return this.insights[this.config.filterTab];
  };

  Personetics.utils.persoInboxReporter.getInboxReporterInsight = function (insightId) {
    var insights = this.getInboxReporterInsights();
    return insights && Object.keys(insights).length ? insights[insightId] : null;
  };

  Personetics.utils.persoInboxReporter.convertInsightsElmArrayToObject = function (array) {
    var obj = {};
    var insightId = null;

    if (array && array.length) {
      array.map(function (index, data) {
        insightId = data ? data.getAttribute('data-id') : null;

        if (insightId) {
          obj[insightId] = data;
        }
      });
    }

    return obj;
  };

  Personetics.utils.persoInboxReporter.convertInsightArrayToObject = function (insights) {
    var self = this;
    var insightsObj = {};

    if (insights && insights.length) {
      insights.map(function (insight) {
        insight[self.config.presented] = false;
        insightsObj[insight.id] = insight;
      });
    }

    return insightsObj;
  };

  Personetics.utils.persoInboxReporter.notifyReportEvent = function (insight, eventType, value) {
    var params = {
      type: eventType,
      value: value,
      insightId: insight.insightId,
      instanceId: insight.id
    };
    var isReportingAnalyticsOn = Personetics.projectConfiguration.getConfig('analytics');
    Personetics.utils.PEventReporting.setValue(params);
    Personetics.utils.PEventReporting.notifyEvent();

    if (isReportingAnalyticsOn) {
      Personetics.utils.PEventReporting.notifyAnalyticsEvent();
    }
  };

  Personetics.utils.persoInboxReporter.markInsightAs = function (insightId, attr, status) {
    var insights = this.getInboxReporterInsights();

    if (insights && insights[insightId]) {
      insights[insightId][attr] = status;
    }
  };

  Personetics.utils.persoInboxReporter.checkInsightReportStatus = function (insightId, attr) {
    var insights = this.getInboxReporterInsights();
    return insights && insights[insightId] ? insights[insightId][attr] : false;
  };

  Personetics.utils.persoInboxReporter.isBlockPositionInViewport = function (insightId) {
    var containerEl = this.container;
    var insightElm = this.container.find(this.config.$persoTeaserTemplate + insightId);

    if (insightElm && insightElm.length) {
      var docViewTop = containerEl.scrollTop();
      var docViewBottom = docViewTop + containerEl.height();
      var blockTop = $(insightElm).offset().top - containerEl.offset().top;
      var blockBottom = blockTop + $(insightElm).height();
      return blockBottom <= docViewBottom;
    }

    return false;
  };

  Personetics.utils.persoInboxReporter.handleInsight = function (insightId) {
    var reporterInsight = this.getInboxReporterInsight(insightId);

    if (reporterInsight) {
      this.notifyReportEvent(reporterInsight, this.config.presentedEventType, this.config.value);
      this.markInsightAs(insightId, this.config.presented, true);
    }
  };

  Personetics.utils.persoInboxReporter.handleInboxReporter = function () {
    var ignorePresented = false;
    var insightElms = this.insightElms[this.config.filterTab];

    for (var insight in insightElms) {
      if (!this.checkInsightReportStatus(insight, this.config.presented)) {
        if (!ignorePresented && this.isBlockPositionInViewport(insight)) {
          this.handleInsight(insight);
        } else {
          break;
        }
      }

      this.insightsQueue--;
      delete insightElms[insight];
    }
  };

  Personetics.utils.persoInboxReporter.onScrollInboxReporter = function (onScroll) {
    var scrollDown = onScroll && onScroll.scrollTop > this.inboxScrollTop ? true : false;

    if (this.insightsQueue && scrollDown) {
      this.inboxScrollTop = onScroll.scrollTop;
      this.handleInboxReporter(scrollDown);
    }
  };

  Personetics.utils.persoInboxReporter.initializeInboxReporter = function (inboxElm) {
    if (inboxElm && inboxElm.length) {
      this.inboxScrollTop = 0;
      this.container = inboxElm;
      var filterTab = this.config.filterTab;
      this.insightElms[filterTab] = this.insightElms[filterTab]
        ? this.insightElms[filterTab]
        : this.convertInsightsElmArrayToObject(inboxElm.find(this.config.$teaserElement));
      this.insightsQueue =
        this.insightElms && this.insightElms[filterTab]
          ? Object.keys(this.insightElms[filterTab]).length
          : 0;

      if (this.insightsQueue) {
        this.handleInboxReporter();
      }
    }
  };

  Personetics.utils.persoInboxReporter.handleCarouselReporter = function (insight) {
    var insightElm = $(insight);
    var insightId = insightElm.length ? insightElm.attr('data-id') : null;

    if (insightId && !this.checkInsightReportStatus(insightId, this.config.presented)) {
      this.handleInsight(insightId);
    }
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.messenger = Personetics.messenger || {};
  Personetics.messenger.MSG_SECURITY_ERROR = 'perso-story-widget';
  Personetics.messenger.MSG_RENDERED_STORY = 'perso-rendered-story-widget';

  Personetics.messenger.listen = function listen(msg, cb) {
    $(this).bind(msg, cb);
  };

  Personetics.messenger.removeListener = function removeListener(msg, cb) {
    $(this).unbind(msg, cb);
  };

  Personetics.messenger.dispatch = function dispatch(msg, data) {
    $(this).trigger(msg, data);
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoMultiLineEllipsis = Personetics.utils.persoMultiLineEllipsis || {};

  Personetics.utils.persoMultiLineEllipsis.checkElementOverflow = function (el) {
    if (el.scrollHeight > el.offsetHeight + 1) {
      // Add 1px to element offset height since we are using em and might have rounding issues.
      return true;
    }

    return false;
  };

  Personetics.utils.persoMultiLineEllipsis.clipWordAddEllipsis = function (str) {
    if (typeof str === 'string') {
      var strArr = str.split(' ');
      strArr.pop();
      strArr[strArr.length - 1] = strArr[strArr.length - 1] + '...';
      str = strArr.join(' ');
    }

    return str;
  };

  Personetics.utils.persoMultiLineEllipsis.clipLetterAddEllipsis = function (str) {
    if (typeof str === 'string') {
      str = str.slice(0, -4);

      if (str.substr(str.length - 1) === ' ') {
        // Avoid space before 3 dots
        str = str.slice(0, -1);
      }

      str = str + '...';
    }

    return str;
  };

  Personetics.utils.persoMultiLineEllipsis.generateEllipsisString = function (el, clipLettersMode) {
    try {
      if (el.length || this.checkElementOverflow(el)) {
        if (typeof clipLettersMode !== 'boolean') {
          clipLettersMode = false;
        }

        var initialString = el.innerText;
        var max = 150; // Max loop iterations -  max words/letters to clip

        var x = 0;

        while (this.checkElementOverflow(el) && x < max) {
          el.innerText = clipLettersMode
            ? this.clipLetterAddEllipsis(el.innerText)
            : this.clipWordAddEllipsis(el.innerText);
          x = x + 1;
        }

        if (x === max)
          throw (
            'Too many iterations,' + initialString + ' string is too long for ellipsis clipping'
          );
      }

      return el;
    } catch (e) {
      console.error(e);
    }
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.projectConf = Personetics.utils.projectConf || {};

  Personetics.utils.projectConf.verifyConfig = function (config, defaultConfig) {
    if (_typeof(Personetics.projectConfiguration.getConfig(config)) === _typeof(defaultConfig)) {
      return Personetics.projectConfiguration.getConfig(config);
    } else {
      if (typeof Personetics.projectConfiguration.getConfig(config) !== 'undefined') {
        console.error(
          'Wrong assignment in projectConfiguration.setConfig to the config ' +
            config +
            ', value must be a ' +
            _typeof(defaultConfig) +
            ' type.'
        );
      }

      return defaultConfig;
    }
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.remoteAssets = {};

  Personetics.utils.remoteAssets.finishedLoading = function (imgEl) {
    $(imgEl).removeClass('perso-asset-loading');
    $(imgEl).siblings('.perso-asset-large-loader').remove();
    $(imgEl).show();
  };

  Personetics.utils.remoteAssets.setDefaultAsset = function (imgEl, defaultAsset) {
    Personetics.utils.remoteAssets.finishedLoading(imgEl);
    $(imgEl).attr('src', defaultAsset);
  };

  Personetics.utils.remoteAssets.setAssetLoadingTriesAttr = function (imgEl) {
    var inc = $(imgEl).data('data-asset-loading-tries') || 0;
    inc += 1;
    $(imgEl).data('data-asset-loading-tries', inc);
  };

  Personetics.utils.remoteAssets.removeAssetLoadingTriesAttr = function (imgEl) {
    $(imgEl).removeAttr('data-asset-has-error');
  };

  Personetics.utils.remoteAssets.getEBLocalBaseUrl = function (widgetType) {
    var LocalBaseUrlMappingEB = {
      story: './PStoryJS/Directive/personetics-editor/resources/assets',
      teaser: '../../PStoryJS/Directive/personetics-editor/resources/assets'
    };
    return LocalBaseUrlMappingEB[widgetType];
  };

  Personetics.utils.remoteAssets.getWrapperLocalBaseUrl = function (wrapperType) {
    var localBaseUrlMappingWrappers = {
      Angular: './personetics-pkg-assets',
      RJS: '/personetics-pkg-assets'
    };
    return localBaseUrlMappingWrappers[wrapperType];
  };

  Personetics.utils.remoteAssets.setAssetsConfig = function (config) {
    var localBaseUrl = Personetics.projectConfiguration.getConfig('localBaseUrl');

    if (config) {
      Personetics.projectConfiguration.setConfig({
        remoteBaseUrl: config.baseUrl,
        useRemoteAssets: config.useRemoteAssets,
        localBaseUrl: localBaseUrl
      });
    }
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (root, factory) {
  if (typeof define === 'function' && define.amd) {
    define(factory);
  } else if ((typeof exports === 'undefined' ? 'undefined' : _typeof(exports)) === 'object') {
    module.exports = factory();
  } else {
    root.PersoResizeDetector = factory();
  }
})(typeof window !== 'undefined' ? window : this, function () {
  // Make sure it does not throw in a SSR (Server Side Rendering) situation
  if (typeof window === 'undefined') {
    return null;
  } // Only used for the dirty checking, so the event callback count is limited to max 1 call per fps per sensor.
  // In combination with the event based resize sensor this saves cpu time, because the sensor is too fast and
  // would generate too many unnecessary events.

  var requestAnimationFrame =
    window.requestAnimationFrame ||
    window.mozRequestAnimationFrame ||
    window.webkitRequestAnimationFrame ||
    function (fn) {
      return window.setTimeout(fn, 20);
    };
  /**
   * Iterate over each of the provided element(s).
   *
   * @param {HTMLElement|HTMLElement[]} elements
   * @param {Function}                  callback
   */

  function forEachElement(elements, callback) {
    var elementsType = Object.prototype.toString.call(elements);
    var isCollectionTyped =
      '[object Array]' === elementsType ||
      '[object NodeList]' === elementsType ||
      '[object HTMLCollection]' === elementsType ||
      '[object Object]' === elementsType ||
      ('undefined' !== typeof jQuery && elements instanceof jQuery) ||
      ('undefined' !== typeof Elements && elements instanceof Elements); //mootools

    var i = 0,
      j = elements.length;

    if (isCollectionTyped) {
      for (; i < j; i++) {
        callback(elements[i]);
      }
    } else {
      callback(elements);
    }
  }
  /**
   * Get element size
   * @param {HTMLElement} element
   * @returns {Object} {width, height}
   */

  function getElementSize(element) {
    if (!element.getBoundingClientRect) {
      return {
        width: element.offsetWidth,
        height: element.offsetHeight
      };
    }

    var rect = element.getBoundingClientRect();
    return {
      width: Math.round(rect.width),
      height: Math.round(rect.height)
    };
  }
  /**
   * Class for dimension change detection.
   *
   * @param {Element|Element[]|Elements|jQuery} element
   * @param {Function} callback
   *
   * @constructor
   */

  var PersoResizeDetector = function PersoResizeDetector(element, callback) {
    /**
     *
     * @constructor
     */
    function EventQueue() {
      var q = [];

      this.add = function (ev) {
        q.push(ev);
      };

      var i, j;

      this.call = function () {
        for (i = 0, j = q.length; i < j; i++) {
          q[i].call();
        }
      };

      this.remove = function (ev) {
        var newQueue = [];

        for (i = 0, j = q.length; i < j; i++) {
          if (q[i] !== ev) newQueue.push(q[i]);
        }

        q = newQueue;
      };

      this.length = function () {
        return q.length;
      };
    }
    /**
     *
     * @param {HTMLElement} element
     * @param {Function}    resized
     */

    function attachResizeEvent(element, resized) {
      if (!element) return;

      if (element.resizedAttached) {
        element.resizedAttached.add(resized);
        return;
      }

      element.resizedAttached = new EventQueue();
      element.resizedAttached.add(resized);
      element.persoResizeDetector = document.createElement('div');
      element.persoResizeDetector.dir = 'ltr';
      element.persoResizeDetector.className = 'perso-resize-detector';
      var style =
        'position: absolute; left: -10px; top: -10px; right: 0; bottom: 0; overflow: hidden; z-index: -1; visibility: hidden;';
      var styleChild = 'position: absolute; left: 0; top: 0; transition: 0s;';
      element.persoResizeDetector.style.cssText = style;
      element.persoResizeDetector.innerHTML =
        '<div class="perso-resize-detector-expand" style="' +
        style +
        '">' +
        '<div style="' +
        styleChild +
        '"></div>' +
        '</div>' +
        '<div class="perso-resize-detector-shrink" style="' +
        style +
        '">' +
        '<div style="' +
        styleChild +
        ' width: 200%; height: 200%"></div>' +
        '</div>';
      element.appendChild(element.persoResizeDetector);
      var position = window.getComputedStyle(element).getPropertyPriority('position');

      if ('absolute' !== position && 'relative' !== position && 'fixed' !== position) {
        element.style.position = 'relative';
      }

      var expand = element.persoResizeDetector.childNodes[0];
      var expandChild = expand.childNodes[0];
      var shrink = element.persoResizeDetector.childNodes[1];
      var dirty, rafId, newWidth, newHeight;
      var size = getElementSize(element);
      var lastWidth = size.width;
      var lastHeight = size.height;

      var reset = function reset() {
        //set display to block, necessary otherwise hidden elements won't ever work
        var invisible = element.offsetWidth === 0 && element.offsetHeight === 0;

        if (invisible) {
          var saveDisplay = element.style.display;
          element.style.display = 'block';
        }

        expandChild.style.width = '100000px';
        expandChild.style.height = '100000px';
        expand.scrollLeft = 100000;
        expand.scrollTop = 100000;
        shrink.scrollLeft = 100000;
        shrink.scrollTop = 100000;

        if (invisible) {
          element.style.display = saveDisplay;
        }
      };

      element.persoResizeDetector.resetSensor = reset;

      var onResized = function onResized() {
        rafId = 0;
        if (!dirty) return;
        lastWidth = newWidth;
        lastHeight = newHeight;

        if (element.resizedAttached) {
          element.resizedAttached.call();
        }
      };

      var onScroll = function onScroll() {
        var size = getElementSize(element);
        var newWidth = size.width;
        var newHeight = size.height;
        dirty = newWidth != lastWidth || newHeight != lastHeight;

        if (dirty && !rafId) {
          rafId = requestAnimationFrame(onResized);
        }

        if (element.resizedAttached) {
          element.resizedAttached.call();
        }

        reset();
      };

      var addEvent = function addEvent(el, name, cb) {
        if (el.attachEvent) {
          el.attachEvent('on' + name, cb);
        } else {
          el.addEventListener(name, cb);
        }
      };

      addEvent(expand, 'scroll', onScroll);
      addEvent(shrink, 'scroll', onScroll); // Fix for custom Elements

      requestAnimationFrame(reset);
    }

    forEachElement(element, function (elem) {
      attachResizeEvent(elem, callback);
    });

    this.detach = function (ev) {
      PersoResizeDetector.detach(element, ev);
    };

    this.reset = function () {
      element.persoResizeDetector.resetSensor();
    };
  };

  PersoResizeDetector.reset = function (element, ev) {
    forEachElement(element, function (elem) {
      elem.persoResizeDetector.resetSensor();
    });
  };

  PersoResizeDetector.detach = function (element, ev) {
    forEachElement(element, function (elem) {
      if (!elem) return;

      if (elem.resizedAttached && typeof ev === 'function') {
        elem.resizedAttached.remove(ev);
        if (elem.resizedAttached.length()) return;
      }

      if (elem.persoResizeDetector) {
        if (elem.contains(elem.persoResizeDetector)) {
          elem.removeChild(elem.persoResizeDetector);
        }

        delete elem.persoResizeDetector;
        delete elem.resizedAttached;
      }
    });
  };

  window.PersoResizeDetector = PersoResizeDetector;
  return PersoResizeDetector;
});

(function (window, Personetics, $) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoTheming = {};
  Personetics.utils.persoTheming.styleInitiated = false;
  Personetics.utils.persoTheming.classObjStringified = '';

  Personetics.utils.persoTheming.handleSelectorsObj = function handleSelectorsObj(classObjKey) {
    for (var key in Personetics.selectorsObj[classObjKey]) {
      var paramsArr = Personetics.selectorsObj[classObjKey][key];
      var className = '';
      var selector = '';
      var colorCSSAttr = '';
      var extraCSSAttr = '';
      $.each(paramsArr, function (index, elm) {
        switch (index) {
          case 0:
            className = elm;
            break;

          case 1:
            selector = elm;
            break;

          case 2:
            colorCSSAttr = elm;
            break;

          case 3:
            extraCSSAttr = elm;
            break;

          default:
            break;
        }
      });
      this.classObjStringified += this.generateCssString(
        classObjKey,
        className,
        selector,
        colorCSSAttr,
        this.classObj[classObjKey],
        extraCSSAttr
      );
    }
  };

  Personetics.utils.persoTheming.initTheme = function init(classMap) {
    if (personetics.config.darkMode) {
      this.id = '#perso-dark-mode';
    } else if (Personetics.utils.projectConf.verifyConfig('allowExternalTheme', false)) {
      this.id = '#perso-theme';
    }

    if (_typeof(classMap) !== 'object' || classMap === null) {
      console.error('Non-object provided to persoTheming util.');
      return false;
    }

    this.classObj = {};

    for (var key in classMap) {
      this.classObj[key] = classMap[key];
    }

    this.createStyleTag();
  };

  Personetics.utils.persoTheming.createStyleTag = function createStyleTag() {
    var style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = this.createClasses();
    document.getElementsByTagName('head')[0].appendChild(style);
    Personetics.utils.persoTheming.styleInitiated = true;
  };

  Personetics.utils.persoTheming.validateColor = function validateColor(color, key) {
    if (typeof color !== 'string') {
      console.error('Non string value provided to theme key ', key);
      return false;
    }

    var testEl = new Option().style;
    testEl.color = color;

    if (testEl.color === '') {
      console.error('Invalid color value provided to theme key', key);
      return false;
    }

    return true;
  };

  Personetics.utils.persoTheming.generateCssString = function generateCssString(
    configName,
    className,
    extraCssSelectors,
    cssAttr,
    configValue,
    extraCss
  ) {
    if (!extraCss) {
      extraCss = '';
    }

    return (
      this.id +
      ' .perso-theme-' +
      configName +
      '-' +
      className +
      ' ' +
      extraCssSelectors +
      '{' +
      cssAttr +
      ':' +
      configValue +
      ';' +
      extraCss +
      '}\n'
    );
  };

  Personetics.utils.persoTheming.createClasses = function createClasses() {
    for (var key in this.classObj) {
      if (this.validateColor(this.classObj[key], key)) {
        //background
        this.classObjStringified += this.generateCssString(
          key,
          'background',
          '',
          'background-color',
          this.classObj[key]
        ); // color

        this.classObjStringified += this.generateCssString(
          key,
          'color',
          '',
          'color',
          this.classObj[key]
        );

        if (key === 'inflowColor' || key === 'outflowColor') {
          // inflowColor/outflowColor background-selected for comparable chart - applied on perso-bars-wrapper element
          var flowClass = key === 'inflowColor' ? '.perso-in' : '.perso-out';
          this.classObjStringified += this.generateCssString(
            'comparable',
            'selected-background',
            '.perso-series-wrapper .perso-series .perso-bar-wrapper' +
              flowClass +
              ' .perso-bar.perso-selected:not(.perso-selected-animated)',
            'background-color',
            this.classObj[key]
          );
          this.classObjStringified += this.generateCssString(
            'comparable',
            'selected-background',
            '.perso-series-wrapper .perso-series .perso-bar-wrapper' +
              flowClass +
              ' .perso-bar.perso-selected.perso-selected-animated .perso-filler-bar',
            'background-color',
            this.classObj[key]
          ); // inflowColor/outflowColor background-selected for bar chart - applied on perso-bar-chart-wrapper wrraper element

          var barChartClass = key === 'inflowColor' ? ':not(perso-negative)' : '.perso-negative';
          this.classObjStringified += this.generateCssString(
            'bar-chart',
            'selected-background',
            '.perso-bar-chart-container' +
              barChartClass +
              ' .perso-bars-wrapper .perso-bar-wrapper.perso-simple-bar .perso-bar.perso-selected:not(.perso-selected-animated)',
            'background-color',
            this.classObj[key]
          );
          this.classObjStringified += this.generateCssString(
            'bar-chart',
            'selected-background',
            '.perso-bar-chart-container' +
              barChartClass +
              ' .perso-bars-wrapper .perso-bar-wrapper.perso-simple-bar .perso-bar.perso-selected.perso-selected-animated .perso-filler-bar',
            'background-color',
            this.classObj[key]
          ); // inflowColor/outflowColor - teaser horizontal bars colors

          var teaserComparableClass = key === 'inflowColor' ? '.In' : '.Out';
          this.classObjStringified += this.generateCssString(
            'teaser',
            'horizontal-bars',
            '.perso-svg-chart-container.multiHorizontal .perso-chart-rect' + teaserComparableClass,
            'fill',
            this.classObj[key]
          ); // inflowColor/outflowColor - SCR bold answers colors

          this.classObjStringified += this.generateCssString(
            key,
            'background-answer',
            '.perso-bold',
            'background-color',
            this.classObj[key]
          );
          var triviaAnswer =
            key === 'inflowColor' ? '.perso-answer-correct' : '.perso-answer-incorrect';
          this.classObjStringified += this.generateCssString(
            key,
            'background-answer',
            '.perso-answer-icon' + triviaAnswer,
            'background-color',
            this.classObj[key]
          );
        }

        this.handleSelectorsObj(key);
      }
    }

    return this.classObjStringified;
  };

  Personetics.utils.persoTheming.validateDOMElement = function validateDOMElement(obj) {
    return (typeof HTMLElement === 'undefined' ? 'undefined' : _typeof(HTMLElement)) === 'object'
      ? obj instanceof HTMLElement
      : obj &&
          _typeof(obj) === 'object' &&
          obj !== null &&
          obj.nodeType === 1 &&
          typeof obj.nodeName === 'string';
  };

  Personetics.utils.persoTheming.addClassToElement = function addClassToElement(element, newClass) {
    if (element.length) {
      for (var i = 0; i < element.length; i++) {
        this.addClassToElement(element[i], newClass);
      }
    } else if (element && this.validateDOMElement(element)) {
      var arr = element.className.split(' ');

      if (arr.indexOf(newClass) === -1) {
        element.className += ' ' + newClass;
      }
    } else {
      Personetics.log('Non HTML DOM element provided to persoTheming.addClassToElement method');
    }
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoValidator = {};

  Personetics.utils.persoValidator.isXssScriptDetected = function isXssScriptDetected(data) {
    var isMatch = false;
    var regex = /(<script|<\/script|<style|url=|url\(|onload|onerror|src=|alert\(|href=|javascript:|redirect [1-9]).+/gi;
    var result = regex.test(data);

    if (result) {
      isMatch = true;
    } else {
      isMatch = false;
    }

    return isMatch;
  };

  Personetics.utils.persoValidator.isValidSelector = function (selector) {
    var span = document.createElement('span');
    span.innerHTML = selector;
    return span.childElementCount ? true : false;
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.persoVersioning = {};

  Personetics.utils.persoVersioning.getPersoCoreVersion = function getPersoCoreVersion() {
    return '21.5';
  };

  Personetics.utils.persoVersioning.getPersoSolutionVersion = function getPersoSolutionVersion() {
    return '';
  };
})(window, (window.Personetics = window.Personetics || {}));

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.scope = {}; // Returns a wrapper that binds "this" inside "fn" to "scope"

  Personetics.utils.scope.bind = function bind(scope, fn) {
    return function () {
      return fn.apply(scope, arguments);
    };
  };

  Personetics.bind = Personetics.utils.scope.bind;

  Personetics.utils.scope.Callback = function Callback(f, parameters) {
    this.parameters = parameters;
    this.f = f;
    var me = this;

    this.invoke = function () {
      me.f.apply(me, me.parameters); // me.f(me.parameters);
    };
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.string = {};

  Personetics.utils.string.isString = function isString(str) {
    return typeof str === 'string';
  };

  Personetics.utils.string.isEmpty = function isString(str) {
    return Personetics.utils.string.isString(str) && str.length == 0;
  };

  Personetics.utils.string.isValidString = function isValidString(str) {
    return Personetics.utils.assert.isString(str) && str != null && str.length > 0;
  };

  Personetics.utils.string.truncateLongWords = function truncateLongWords(setnece, maxWordLength) {
    if (
      typeof setnece !== 'undefined' &&
      setnece &&
      setnece.length > 0 &&
      setnece.replace(/ /g, '').length > 0
    ) {
      var words = setnece.split(' ');
      var processedWords = [];
      $.each(words, function (i, word) {
        if (word.length > maxWordLength) {
          processedWords.push(word.substring(0, maxWordLength - 6) + '...');
        } else {
          processedWords.push(word);
        }
      });
      return processedWords.join(' ');
    } else {
      return '';
    }
  };

  Personetics.utils.string.stripTags = function stripTags(input) {
    var tagsToReplace = ['script'];
    var result = input;

    if (typeof input !== 'undefined' && input != null && input.length > 0) {
      $.each(tagsToReplace, function (i, tagToReplace) {
        var tagRegex = new RegExp('<' + tagsToReplace + '.*>.*</' + tagToReplace + '>', 'ig');
        result = result.replace(tagRegex, '');
      });
    }

    return result;
  };

  Personetics.utils.string.replaceNewLine = function replaceNewLine(element) {
    if (!element) return element;

    var typeOf = _typeof(element);

    if (typeOf == 'string') {
      var regex = new RegExp('^<([A-Z][A-Z0-9]*)[^>]*>(.*?)</\\1>$', 'gim');
      var res = regex.exec(element.replace(/\n/g, ''));

      if (res) {
        element = element.replace(/\n/g, '');
      } //html code
      else {
        element = element.replace(/\n/g, '<br />');
      }
    } else if (typeOf == 'object')
      $.each(element, function (key, value) {
        if (!value) return;
        if (typeof key == 'string' && key.toLowerCase().indexOf('speaking') != -1)
          // skip
          // speaking
          return;

        var typeOfValue = _typeof(value);

        element[key] = Personetics.utils.string.replaceNewLine(value);
      });

    return element;
  };

  Personetics.utils.string.StringBuffer = function StringBuffer() {
    this.buffer = [];

    this.append = function append(string) {
      this.buffer.push(string);
      return this;
    };

    this.toString = function toString() {
      return this.buffer.join('');
    };
  };

  Personetics.utils.string.decodeHTMLSpecialCharecters = function decodeHTMLSpecialCharecters(
    input
  ) {
    var decodedString = input.replace('&amp;', '&');
    /* add here any other replacments if needed in the future*/

    return decodedString;
  };

  Personetics.utils.string.stripSpecialChars = function stripSpecialChars(val) {
    return val.replace(/[^a-z0-9\s]/gi, '').replace(/[_\s]/g, '-');
  };

  Personetics.utils.string.removeClass = function removeClass(className, clazz) {
    var _classname = className; //support objects for svg

    if (_typeof(className) === 'object') _classname = _classname.animVal;
    _classname = _classname.replace(new RegExp('(?:^|\\s)' + clazz + '(?:\\s|$)'), ' ');
    return _classname;
  };

  Personetics.utils.string.addClass = function addClass(className, clazz) {
    className = Personetics.utils.string.removeClass(className, clazz);
    var _classname = className; //support objects for svg

    if (_typeof(className) === 'object') _classname = _classname.animVal;
    _classname += ' ' + clazz;
    return _classname;
  };

  Personetics.utils.string.textWidth = function (text) {
    var div = $('#perso-text-width');
    if (div.length == 0)
      div = $('<div id="perso-text-width" style="display: none;"></div>').appendTo($('body'));
    div.html(text);
    var itemWidth = div.width();
    div.remove();
    return itemWidth;
  };

  Personetics.utils.string.extractContent = function (text) {
    var span = document.createElement('span');
    span.innerHTML = decodeURI(text);
    return span.textContent || span.innerText;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.url = {};
  /**
   * Returns value of specific request parameter identified by the name parameter
   */

  Personetics.utils.url.getParameterByName = function getParameterByName(name) {
    name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
    var regex = new RegExp('[\\?&]' + name + '=([^&#]*)'),
      results = regex.exec(location.search);
    return results == null ? null : decodeURIComponent(results[1].replace(/\+/g, ' '));
  };
  /**
   * Returns full address of given relative URL without the file name and query string
   */

  Personetics.utils.url.getAbsoluteUrl = function getAbsoluteUrl(localurl) {
    if (/^(https?|file|ftps?|mailto|javascript|data:image\/[^;]{2,9};):/i.test(localurl))
      return localurl; // localurl is already absolute

    var href = window.location.href;
    var pathArray = href.split('/');
    var newPathname = ''; //for ( var i = 0; i < 3/*pathArray.length - 1*/; i++) {

    for (var i = 0; i < pathArray.length - 1; i++) {
      if (i != 0) newPathname += '/';
      newPathname += pathArray[i];
    }

    return newPathname + '/' + localurl;
  };
  /**
   * Opens a new browser tab using post request
   */

  Personetics.utils.url.openTabWithPost = function openTabWithPost(localUrl, windowoption, params) {
    var absoluteUrl = Personetics.utils.url.getAbsoluteUrl(localUrl);
    var newWin = window.open('', '_blank');
    newWin.opener = null;
    newWin.focus();
    var newDoc = newWin.document;
    Personetics.utils.network.createPostform(name, params, absoluteUrl, newDoc);
  };
  /**
   * Creates a new iframe and loads it with a post request
   */

  Personetics.utils.url.iframeWithPost = function iframeWithPost(url, name, params) {
    var form = document.createElement('form');
    form.setAttribute('method', 'post');
    form.setAttribute('action', url);
    form.setAttribute('target', name);

    for (var i in params) {
      if (params.hasOwnProperty(i)) {
        var input = document.createElement('input');
        input.type = 'hidden';
        input.name = i;
        input.value = params[i];
        form.appendChild(input);
      }
    }

    document.body.appendChild(form); // note I am using a post.htm page since I did not want to make double
    // request to the page
    // it might have some Page_Load call which might screw things up.
    // window.open("post.htm", name, windowoption);

    form.submit();
    document.body.removeChild(form);
  };

  Personetics.utils.url.urlCheck = function urlCheck(s) {
    var regexp = /(ftp|http|https):\/\/(\w+:{0,1}\w*@)?(\S+)(:[0-9]+)?(\/|\/([\w#!:.?+=&%@!\-\/]))?/;
    return regexp.test(s);
  };

  Personetics.utils.url.encodePostParams = function encodePostParams(parameterObject) {
    var postParamObject = {};
    /* if contains in key the string 64 - encode it and add it to the object */

    $.each(
      parameterObject,
      Personetics.bind(this, function (key, value) {
        if (key.indexOf('64') >= 0) {
          /*add base64 encoding*/
          var paramsJson = Personetics.utils.json.toJSON(parameterObject[key]);
          var str64 = Personetics.utils.encodeDecode.Base64.encode(paramsJson);
          parameterObject[key] = str64;
        }
      })
    );
    return parameterObject;
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  Personetics.utils.PEvent = Base.extend({
    initialize: function initialize() {
      this.eventMessage = {
        type: 'sendEvents',
        eventInfo: {},
        checkInPastEvent: true
      };
    },
    // eventMessage: {
    //     "type" : "notifyEvent",
    //     "eventInfo": {},
    //     "checkInPastEvent": true
    // },
    setParam: function setParam(paramName, paramValue) {
      this.eventMessage.eventInfo.params[paramName] = paramValue;
    },
    getJsonMessageEvent: function getJsonMessageEvent() {
      return JSON.stringify(this.eventMessage);
    },
    onSuccessCallback: function onSuccessCallback() {},
    onErrorCallback: function onErrorCallback() {}
  });
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};

  Personetics.utils.PEventsBuffer = function () {
    this.sendEventsTypesMapping = {
      'account-selector': 'AccountSelector',
      'bar-chart': 'BarChart',
      'pie-chart': 'PieChart',
      tabs: 'Button',
      buttons: 'Button',
      Navigation: 'NavigateButton'
    };
    /* Event that were sent to PServer*/

    this.pastEvents = [];
    /* Event that are in Q and waiting to be sent to PServer*/

    this.eventToSend = [];
    /* Delay (seconds) between each time we send all events in "eventToSend" to PServer */

    this.eventDelayInterval = Personetics.projectConfiguration.getConfig('eventDelayInterval');

    this.start = function () {
      var me = this;
      this.intervalId = setInterval(function () {
        me.sendEvents();
      }, this.eventDelayInterval);
    };

    this.stop = function () {
      if (this.intervalId) clearInterval(this.intervalId);
    };
    /* Adding events to the "eventToSend" Q */

    this.addEventToSend = function (event, success_callback, error_callback) {
      // Check if event already registered
      var shouldAddEvent = this.shouldSendEvent(event);

      if (shouldAddEvent == true) {
        this.eventToSend.push({
          eventData: event,
          onSuccessCallback: success_callback,
          onErrorCallback: error_callback
        });

        if (
          event.eventMessage.eventInfo.type === 'Navigation' ||
          event.eventMessage.eventInfo.type === 'BudgetCategoryActivation' ||
          event.eventMessage.eventInfo.type === 'BudgetCategoryDeActivation' ||
          event.eventMessage.eventInfo.type === 'BudgetAmount'
        ) {
          this.sendEvents();
        }
      }
    };
    /* Send all events in buffer */

    this.sendEvents = function () {
      var me = this;
      var didSendEvents = false;
      var requestMsg = {};
      var eventsInfoList = [];

      if (this.eventToSend.length) {
        requestMsg.type = this.eventToSend[0].eventData.eventMessage.type;
        requestMsg.lang = Personetics.processor.PStoryConfig.getConfig('lang');
        requestMsg.protocolVersion = this.eventToSend[0].eventData.eventMessage.protocolVersion;
      }

      $.each(this.eventToSend, function (key, pevent) {
        if (
          typeof me.sendEventsTypesMapping[pevent.eventData.eventMessage.eventInfo.type] !==
          'undefined'
        ) {
          pevent.eventData.eventMessage.eventInfo.type =
            me.sendEventsTypesMapping[pevent.eventData.eventMessage.eventInfo.type];
        }

        eventsInfoList.push(pevent.eventData.eventMessage.eventInfo);
        /* Add event to Q - pastEvents */

        me.pastEvents.push(pevent);
      });

      if (eventsInfoList.length) {
        didSendEvents = true;
        requestMsg.eventsInfoList = eventsInfoList;
        personetics.pserverProxy.notifyEvent(requestMsg, this.onSuccess, this.onError);
      }
      /* Remove all event from Q - eventToSend */

      if (didSendEvents == true) {
        this.eventToSend = [];
      }
    };

    this.onSuccess = function () {};

    this.onError = function () {};
    /*
     Check if event should be added to the "sending Q"
     The function will check both Qs - "eventToSend" & "pastEvents"
     In case the current event exist - it wont send it
     */

    this.shouldSendEvent = function (pevent) {
      /*
       Go over all the events that were already been sent -
       if the current event is equal - Dont send it again.
       */
      var shouldSendEvent = true;
      var currentEventInfo = JSON.stringify(pevent.eventMessage.eventInfo);

      if (pevent.eventMessage.checkInPastEvent == true) {
        $.each(this.pastEvents, function (key, aPastEvent) {
          var pastEventInfo = aPastEvent.eventData.eventMessage.eventInfo;

          if (JSON.stringify(pastEventInfo) === currentEventInfo) {
            shouldSendEvent = false;
            return false;
          }
        });
      }
      /*
       In case the event was sent - stop the search and return - false = DONT SEND AGAIN
       else continue searching in the second Q
       */

      if (shouldSendEvent == false) return shouldSendEvent;
      /*
       Go over all the events that are in the Q and about to be send  -
       if the current event is equal - Dont add it again.
       */

      $.each(this.eventToSend, function (key, anEventToSend) {
        var pastEventInfo = anEventToSend.eventData.eventMessage.eventInfo;

        if (JSON.stringify(pastEventInfo) === JSON.stringify(currentEventInfo)) {
          shouldSendEvent = false;
          return false;
        }
      });
      return shouldSendEvent;
    };

    this.onEventSendSuccess = function () {
      //TODO: ?
    };

    this.onEventSendError = function () {
      Personetics.log('Error on sending events to PServer....');
    };
  };
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  Personetics.utils = Personetics.utils ? Personetics.utils : {};
  var pEventReporting = Personetics.utils.PEvent.extend({
    initialize: function initialize() {
      this.params = {
        eventType: 'sendEvents',
        checkInPastEvent: 'false',
        protocolVersion: Personetics.projectConfiguration.getConfig('protocolVersion'),
        userId: '',
        type: '',
        value: '',
        insightId: '',
        instanceId: ''
      };
    },
    setValue: function setValue(parameters) {
      var me = this;
      if (_typeof(parameters) !== 'object' || parameters === null) return false;
      $.each(parameters, function (key, value) {
        if (me.params.hasOwnProperty(key) && value != 'undefined' && value != null) {
          me.params[key] = value;
        }
      });
    },
    createMessage: function createMessage() {
      var params = this.params;
      var eventMessage = {
        type: params.eventType,
        userId: params.userId,
        checkInPastEvent: params.checkInPastEvent,
        protocolVersion: params.protocolVersion,
        eventInfo: {
          type: params.type,
          eventDateTime: this.getFormattedDate(),
          params: {
            value: params.value,
            insightId: params.insightId,
            instanceId: params.instanceId
          }
        }
      };
      return eventMessage;
    },
    notifyEvent: function notifyEvent() {
      var pevent = {
        eventMessage: this.createMessage()
      };
      personetics.notifyEvent(pevent);
    },
    notifyAnalyticsEvent: function notifyAnalyticsEvent() {
      var params = {
        eventType: 'analytics'
      };

      if (personetics && personetics.sendWidgetEvent) {
        personetics.sendWidgetEvent(null, params);
      }
    },
    getFormattedDate: function getFormattedDate() {
      var currentDate = new Date();
      var currentDateString =
        currentDate.getUTCMonth() +
        1 +
        '/' +
        currentDate.getUTCDate() +
        '/' +
        currentDate.getUTCFullYear() +
        ' ' +
        currentDate.getUTCHours() +
        ':' +
        currentDate.getUTCMinutes() +
        ':' +
        currentDate.getUTCSeconds();
      return currentDateString;
    }
    /*getJsonMessageEvent: function () {
        return JSON.stringify(this.eventMessage);
    },
     onSuccessCallback: function(){
    },
     onErrorCallback: function(){
    }*/
  });
  Personetics.utils.PEventReporting = new pEventReporting();
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $, undefined) {
  var dict = function dict() {
    var _en;

    this.texts = {
      en:
        ((_en = {
          lblaccSelectAll: 'All accounts',
          lblaccSelectAccounts: 'Accounts',
          lblaccSelectChoose: 'Choose account',
          lblaccSelectAccount: 'Account No.',
          lblaccSelectAccountAccessibility: 'Account number',
          lblAccessibiliotyAccountSelectorText: 'This is an account selector',
          lblChooseAccount: 'choose account to view',
          lbljanuary: 'January',
          lblfebruary: 'February',
          lblmarch: 'March',
          lblapril: 'April',
          lblmay: 'May',
          lbljune: 'June',
          lbljuly: 'July',
          lblaugust: 'August',
          lblseptember: 'September',
          lbloctober: 'October',
          lblnovember: 'November',
          lbldecember: 'December',
          lbljanShort: 'JAN',
          lblfebShort: 'FEB',
          lblmarShort: 'MAR',
          lblaprShort: 'APR',
          lblmayShort: 'MAY',
          lbljunShort: 'JUN',
          lbljulShort: 'JUL',
          lblaugShort: 'AUG',
          lblsepShort: 'SEP',
          lbloctShort: 'OCT',
          lblnovShort: 'NOV',
          lbldecShort: 'DEC',
          lblstoryRating: 'How helpful was this insight?',
          lblfeedbackprompt1: "What didn't you like about this insight?",
          lblfeedbackprompt2: "What didn't you like about this insight?",
          lblfeedbackprompt3: 'Tell us your opinion of this insight',
          lblfeedbackprompt4: 'Great! Tell us why you liked this insight',
          lblfeedbackprompt5: 'Great! Tell us why you liked this insight',
          lblfeedbackdoubleclick: ' ,double tap to edit',
          lblfeedbackpressclick: ' ,press enter to edit',
          btnsubmit: 'Submit',
          lblreactionFeedback: 'Thank you for the feedback!',
          txlistAccNumber: 'Account',
          txlistDescription: 'Transaction',
          txlistDate: 'Date',
          txlistAmount: 'Amount',
          lblgoToInbox: 'View All',
          lbllogout: 'Log Out',
          lblmode: 'Mode',
          lblpreviewMode: 'Preview',
          lblbubbleMode: 'Bubble',
          ttlinbox: 'Inbox',
          ttlinboxstory: 'Insights Inbox',
          ttlinboxAll: 'All',
          ttlinboxUnread: 'Unread',
          ttlinboxAllInsights: 'All insights',
          ttlinboxUnreadInsights: 'Unread insights',
          ttlinboxFlagged: 'Flagged',
          ttlinboxHighlighted: 'Highlighted',
          insightsNoInsighsTexts: 'Nothing to report',
          insightsTitle: 'Insights',
          insightsOptOutTeaserText: "You've chosen not to receive insights",
          insightsLoadInsights: 'Checking for Insights',
          ttlsettings: 'Advanced Settings',
          lblsettingsServer: 'Server',
          lblsettingsChannel: 'Channel',
          lblpasswordMode: 'Password Required',
          ttlloginPage: 'Sign In',
          lbluser: 'User',
          lblpassword: 'Password',
          btnlogin: 'Continue',
          btnadvanced: 'Advanced',
          insightsNoInsightsAccessibilityText: 'There are no new insights',
          insightsOptOutTeaserAccessibilityText: 'You have chosen not to receive insights',
          accessFloatingButton: 'Launcher',
          insightsErrorText: "There's been a problem loading Insights. Check back soon.",
          storyErrorText: "There's been a problem loading story. Check back soon.",
          accessibilityStoryBackButtonText: 'back',
          teaserErrorText: "There's been a problem loading teaser. Check back soon",
          accessibilityPieTitle: 'This is a Pie chart – it shows a breakdown of sums',
          accessibilityNextCategory: 'next category.',
          accessibilityPreviousCategory: 'previous category.',
          accessibilitySelectedCategoryTitle: 'Selected category',
          accessibilitySliceLbl: 'slice',
          accessibilityOfLbl: 'of',
          accessibilityPage: 'page',
          accessibilityTriviaAnswerDoubleTab: 'double tap to select',
          accessibilityAnswer: 'answer',
          accessibilityCorrect: 'correct',
          accessibilityWrongAnswer: 'wrong',
          accessibilityBarChartTitle: 'this is a bar chart',
          accessibilityCalendarTitle:
            'This is a calendar showing upcoming activity , activity dates are highlighted',
          accessibilityBarChartPureTitle:
            'A dynamic bar chart is displayed, to navigate between the available months in the chart, select the months available.',
          accessibilityPinGraphTitle: 'this is a pin graph',
          accessibilityLineGraphTitle: 'this is a line-chart, it shows a chronological trend',
          accessibilityLineGraphHighlightedDot: 'highlighted dot',
          accessibilitySeries: 'series',
          accessibilitySeriesIn: 'series in',
          accessibilitySeriesOut: 'series out',
          accessibilityOutOfTxt: 'out of',
          accessibilitySelected: 'selected',
          accessibilityNotSelected: 'not selected',
          accessibilityUnSelected: 'unselected',
          accessibilityHighlighted: 'highlighted',
          accessibilityPinGraph:
            'A pin chart is displayed, it specifies a list of transactions’ amounts per date.',
          accessibilityTableTitle: 'This table presents a list of transactions',
          accessibilityTableStart: 'Table start',
          accessibilityTransactionOpenModal: 'show more options',
          accessibilityTableEnd: 'Table end.',
          accessibilityRatingLevelTitle: 'rating level',
          accessibilityRadioButton: 'Radio button',
          accessibilityChecked: 'checked',
          accessibilityUnchecked: 'unchecked',
          multiTeasersTitleText: 'Smart Engage',
          accessibilityNextTeaser: 'double tap to view the next insight',
          accessibilityNextMonthDoubleClick: 'double tap to select',
          accessibilityPrevTeaser: 'click to select prev teaser',
          lblbalance: 'Balance',
          lblaccSelectNextAcc: 'show next account',
          lblaccSelectPreviousAcc: 'show previous account',
          accessibilityIntroducePersonetics:
            'Story: Personetics Engage is a new breed of banking solution that puts your needs first. It provides timely and useful insights that keep you informed and help you stay on top of your financial affairs.  Account Activity: We will discover your account activity trends, highlight important events, and flag unusual events.Cash flow forecast: We will predict your cash flow, inform you ahead of time if your balance may not be enough to cover upcoming expenses and remind you about upcoming payments. Financial Education: We will suggest specific steps to increase savings, reduce debt, and improve your financials. In addition, we can help you create and automate a savings plan to meet your financial objectives.  Spending Analysis: We will help you compare your spending between months and across categories, so you can see where your money goes and how your spending changes. Customize your Insights: Get more out of Personetics by rating the insights that you receive. That way, we can adjust what we share with you and focus on what matters most.',
          noInsigthsTextFirstMsg: 'There are no insights for you right now',
          noInsigthsTextSecondMsg: 'We will let you know when you receive new ones',
          nothingToShowHere: "We didn't find anything to show here",
          inboxTechnicalIssues:
            'Something is wrong. Please try again or contact Personetics support',
          lblScheduled: 'Scheduled',
          lblEstimated: 'Estimated',
          lblAnticipated: 'anticipated',
          whoops: 'Whoops',
          invalidInsigthsErrMsg:
            'This insight seems to be invalid.Please Choose a diffrent one or contact Personetics support.',
          wrong: 'Wrong',
          right: 'Right',
          OhNo: 'Oh No!',
          lblIn: 'in',
          lblTotal: 'total',
          lblOut: 'out',
          loading: 'Loading...',
          close: 'Close',
          feedBy: 'Feed by',
          continue: 'Continue',
          previous: 'Previous',
          noJSONsAvailable: 'No JSONs are available in this path',
          chooseInsights: 'Choose one of the insights',
          TryAgain: 'Try Again',
          lblTab: 'tab',
          lblOf: 'of',
          lastPaymentlbl: 'Last Payment: ',
          lblInsight: 'insight',
          closeInboxBtnlbl: 'close inbox button ,double tap to close inbox',
          lblCashflow: 'cashflow',
          lblOutFlows: 'outflows',
          lblInFlows: 'inflows',
          accessibilityCounterBarText: "it's a counter bar"
        }),
        _defineProperty(_en, 'accessibilityCorrect', 'correct'),
        _defineProperty(_en, 'accessibilityInCorrect', 'incorrect'),
        _defineProperty(_en, 'lblPieNextCategory', 'next category is'),
        _defineProperty(_en, 'lblPiePreviousCategory', 'previous category is'),
        _defineProperty(_en, 'lblPieCurrentCategory', 'current category is'),
        _defineProperty(_en, 'lblPieAmount', 'amount'),
        _defineProperty(_en, 'lblPieStaticTitle', 'Total'),
        _defineProperty(_en, 'lblPieStaticAmount', '$0'),
        _defineProperty(_en, 'lblPieNoTransaction', 'No transactions on this month'),
        _defineProperty(_en, 'accessibilityAccountSelectorLeftButton', 'previous account.'),
        _defineProperty(_en, 'accessibilityAccountSelectorRightButton', 'next account.'),
        _defineProperty(_en, 'accessibilityAccountNumber', 'account number'),
        _defineProperty(_en, 'accessibilityAccountName', 'account name'),
        _defineProperty(_en, 'accessibilitySubmittedAnswer', 'your submitted answer:'),
        _defineProperty(_en, 'accessibilityUndo', 'double tap to undo'),
        _defineProperty(_en, 'accessibilityEntitySelector', 'This is an entity selector.'),
        _defineProperty(_en, 'accessibilityESNextButton', 'Next entity.'),
        _defineProperty(_en, 'accessibilityESPreviousButton', 'Previous entity.'),
        _defineProperty(_en, 'accessibilityESSettingsButton', 'Open budget settings, '),
        _defineProperty(_en, 'accessibilityESOutOfTxt', 'out of'),
        _defineProperty(_en, 'accessibilityESButtonTxt', 'Button'),
        _defineProperty(_en, 'accessibilityTransactionModalText', 'Transaction modal opened'),
        _defineProperty(
          _en,
          'accessibilityDropDownCategories',
          'Drop down with categories. Select to change category'
        ),
        _defineProperty(_en, 'accessibilityTagButton', 'Select to change personal tag'),
        _defineProperty(_en, 'accessibilityClearTagButton', 'Delete your personal tag'),
        _defineProperty(_en, 'accessibilityTransactionModalButton', 'button'),
        _defineProperty(_en, 'accessibilityTextInput', 'text input'),
        _defineProperty(_en, 'accessibilityCheckBox', 'checkbox'),
        _defineProperty(_en, 'accessibilityDeleteTag', 'Delete your personal tag.'),
        _defineProperty(
          _en,
          'accessibilityEntityListTitle',
          'This table presents a list of entities'
        ),
        _defineProperty(_en, 'accessibilityEntityListStart', 'table start'),
        _defineProperty(_en, 'accessibilityEntityListEnd', 'table end'),
        _defineProperty(
          _en,
          'accessibilityEntityListDoubleTab',
          'a button, press here, double tap to select'
        ),
        _defineProperty(_en, 'accessibilityOutOfTxtCarousel', 'out of'),
        _defineProperty(_en, 'title', 'Manage Budgets'),
        _defineProperty(_en, 'recommendedTitle', 'Recommended Budget'),
        _defineProperty(_en, 'yourBudgetsTitle', 'Your Budgets'),
        _defineProperty(_en, 'moreBudgetsTitle', 'Add More Budgets'),
        _defineProperty(_en, 'disclaimerTitle', 'Disclaimers'),
        _defineProperty(_en, 'disclaimerheader', 'We want you to know'),
        _defineProperty(
          _en,
          'disclaimerText1',
          'Spending in Your Budgets will never be restricted.'
        ),
        _defineProperty(_en, 'disclaimerText2', 'Your Budgets will be tracked monthly.'),
        _defineProperty(_en, 'disclaimerButton', 'Got it!'),
        _defineProperty(_en, 'recommendedPlaceHolder', 'Recommended'),
        _defineProperty(_en, 'monthlyAvg', 'Monthly Avg.'),
        _defineProperty(_en, 'aboutTxt', 'Your Budgets Info'),
        _defineProperty(_en, 'budgetModalTitleTxt', 'Manage Budgets'),
        _defineProperty(_en, 'stopBudgetTitle', 'Disable Budget'),
        _defineProperty(_en, 'stopBudgetText1', 'Are you sure you want to disable your'),
        _defineProperty(_en, 'stopBudgetText2', 'budget?'),
        _defineProperty(_en, 'stopBudgetbutton1', 'Yes'),
        _defineProperty(_en, 'stopBudgetbutton2', 'No'),
        _defineProperty(_en, 'stopCategoryBudget', 'Stop budget'),
        _defineProperty(_en, 'recommandedLabelIncrease', 'increase last month'),
        _defineProperty(_en, 'recommandedLabelDecrease', 'decrease last month'),
        _defineProperty(_en, 'recommandedLabelMajor', 'one of your major spending categories'),
        _defineProperty(_en, 'inputAmountValidator1', 'Budget amount invalid'),
        _defineProperty(_en, 'confirmButton', 'Confirm'),
        _defineProperty(_en, 'cancelButton', 'Cancel'),
        _defineProperty(_en, 'submitButton', 'Add budget'),
        _defineProperty(_en, 'submittedBudgetRecommended', 'Recommended budget'),
        _defineProperty(_en, 'facebook', 'Facebook link'),
        _defineProperty(_en, 'twitter', 'Twitter link'),
        _defineProperty(_en, 'homepage', 'Home page link'),
        _defineProperty(_en, 'customerSupport', 'Customer support link'),
        _defineProperty(_en, 'dateOutOfRange', 'Enter date from'),
        _defineProperty(_en, 'numberOutOfRange', 'Value range is'),
        _defineProperty(_en, 'to', 'to'),
        _defineProperty(_en, 'dateInput', 'Input date'),
        _defineProperty(_en, 'dateSubmit', 'Submit date'),
        _defineProperty(_en, 'numberInput', 'Input number'),
        _defineProperty(_en, 'numberSubmit', 'Submit number'),
        _defineProperty(_en, 'clearInput', 'Clear number input field'),
        _defineProperty(_en, 'tryAgain', 'Something went wrong, please try again'),
        _defineProperty(_en, 'nonDigit', 'Non-numeric characters are not allowed'),
        _defineProperty(_en, 'filterLearnMore', 'Learn more'),
        _defineProperty(_en, 'filterDoubleTapToSelect', 'Double tap to select'),
        _defineProperty(_en, 'filterDoubleTapToClose', 'Double tap to close'),
        _defineProperty(_en, 'filterToggleSelected', 'Toggle. Selected'),
        _defineProperty(_en, 'filterToggleNotSelected', 'Toggle. Not selected'),
        _defineProperty(_en, 'paymentTxt', 'Payment'),
        _defineProperty(_en, 'outOfTxt', 'Of'),
        _defineProperty(_en, 'budgetModalHeader1', 'Header level one'),
        _defineProperty(_en, 'budgetModalHeader2', 'Header level two'),
        _defineProperty(_en, 'budgetModalClose', 'Close'),
        _defineProperty(
          _en,
          'nonStoryTeaserText',
          'The selected teaser needs your attention, or Choose another teaser to view story'
        ),
        _defineProperty(
          _en,
          'notSelectedTeaserText',
          'Select a notification from your inbox to view the full story'
        ),
        _defineProperty(_en, 'storyTitle', 'Insight'),
        _defineProperty(_en, 'inboxTitle', 'Inbox'),
        _en)
    };
  };

  dict.prototype.getText = function getText(textId, fallbackText) {
    var result = fallbackText || textId;
    var langDict;
    var lang = this.lang;
    if (this.texts.hasOwnProperty(lang)) langDict = this.texts[lang];
    else langDict = this.texts.en;
    if (langDict.hasOwnProperty(textId)) result = langDict[textId];
    if (result == textId)
      Personetics.log(
        "Personetics.dictionary.getText('" +
          textId +
          "', '" +
          lang +
          "') Error: failed to find text"
      );
    return result;
  };

  dict.prototype.overrideLanguageTexts = function overrideLanguageTexts(lang, texts) {
    if (!this.texts.hasOwnProperty(lang)) {
      this.texts[lang] = texts;
    } else {
      for (var textId in texts) {
        if (texts.hasOwnProperty(textId)) {
          this.texts[lang][textId] = texts[textId];
        }
      }
    }
  };

  dict.prototype.setLanguage = function setLanguage(langToConfig) {
    this.lang = langToConfig;
  };

  Personetics.dictionary = new dict();
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $) {
  Personetics.API = Personetics.API || {};
  Personetics.API.PServerProxyNative = Base.extend({
    initialize: function initialize() {
      this.okStatus = '200';
      this.delay = 3000;
      this.requestDataType = 'json';
      this.pserverURL = '';
      this.servletName = '';
      this.requestHeaders = {
        'Content-Type': 'application/json; charset=UTF-8'
      };
      this.requestParams = {};
      this.config = {};
      this.postData = {};
      this.sentRequestsTypeToCallbacks = {};
    },
    start: function start(config) {
      this.config = $.extend(true, this.config, config);
      this.config.clientVersion = Personetics.utils.persoVersioning.getPersoCoreVersion();
      if (this.config.clientVersion) this.postData['clientVersion'] = this.config.clientVersion;
      if (this.config.protocolVersion)
        this.postData['protocolVersion'] = this.config.protocolVersion;
      if (this.config.lang) this.postData['lang'] = this.config.lang;
      if (this.config.deviceType) this.postData['deviceType'] = this.config.deviceType;
      this.requestHeaders = $.extend(true, this.requestHeaders, config.requestHeaders);
      this.requestParams = $.extend(true, this.requestParams, config.requestParams);
      if (config.jwt) this.config.requestHeaders['Authorization'] = 'Bearer ' + config.jwt;
      if (config.userId) this.config.requestHeaders['authToken'] = config.userId;
      this.pserverURL = config.pserverUrl;
      this.base64EncodeResponse = true; // always decode by default

      if (
        typeof config.base64EncodeResponse !== 'undefined' ||
        config.base64EncodeResponse === null
      )
        this.base64EncodeResponse = config.base64EncodeResponse;
      this.requestDataType = this.base64EncodeResponse ? 'text' : 'json';
    },
    getData: function getData(data, successCallback, failureCallback, ignoreGlobalPostData) {
      var postParam = {};
      var postData = data.postData;
      var type = postData.type;

      if (!ignoreGlobalPostData) {
        $.each(this.postData, function (id, name) {
          postParam[id] = name;
        });
        delete this.postData.eventsInfoList;
      }

      $.each(postData, function (id, name) {
        postParam[id] = name;
      }); //add ctxId to specific APIs

      postParam['ctxId'] = Personetics.projectConfiguration.getApiCtxId(
        data.postData.type,
        this.config.ctxId
      );
      var requestId = Personetics.utils.encodeDecode.Base64.encode(
        'reqPersonetics' + type + Date.now()
      );
      var bridge = personetics.getJSBridge();

      if (
        Personetics.utils.assert.isDefined(bridge, false) &&
        Object.prototype.hasOwnProperty.call(bridge, 'sendRequestToPServer') &&
        Personetics.utils.assert.isFunction(bridge.sendRequestToPServer)
      ) {
        this.registerRequestById(requestId, type, successCallback, failureCallback);
        bridge.sendRequestToPServer(this.requestHeaders, postParam, requestId);
      }
    },
    notifyEvent: function notifyEvent(postParams, success, failure) {
      var options = {
        postData: postParams
      };
      this.getData(options, success, failure);
    },
    onError: function onError(errorCallback, errorData) {
      var bridge = personetics.getJSBridge();
      if (
        Object.prototype.hasOwnProperty.call(bridge, 'sessionError') &&
        $.isFunction(bridge.sessionError)
      )
        bridge.sessionError(errorData);

      if (typeof errorCallback !== 'undefined' && $.isFunction(errorCallback)) {
        errorCallback(errorData);
      }
    },
    handlePServerResponse: function handlePServerResponse(data, requestId) {
      if (this.base64EncodeResponse) {
        data = Personetics.utils.encodeDecode.Base64.decode(data);
        data = $.parseJSON(data);
      }

      var success =
        (data && Object.prototype.hasOwnProperty.call(data, 'ok') && data.ok == true) ||
        (data && data.status == 200);

      if (Object.prototype.hasOwnProperty.call(this.sentRequestsTypeToCallbacks, requestId)) {
        var callbacks = this.sentRequestsTypeToCallbacks[requestId];
        if (success == true && typeof callbacks.onSuccess !== 'undefined')
          callbacks.onSuccess(data);
        else {
          this.onError(callbacks.onError, data);
        }
      }
    },
    registerRequestById: function registerRequestById(
      requestId,
      type,
      success_callback,
      error_callback
    ) {
      this.sentRequestsTypeToCallbacks[requestId] = {
        type: type,
        onSuccess: success_callback,
        onError: error_callback
      };
    }
  });
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $) {
  //Personetics.UI = Personetics.UI || {};
  var Handlebars = Personetics.UI.Handlebars;
  Personetics.UI.PWidgetCtrl = Base.extend({
    initialize: function initialize(config) {
      this.config = config;
      this.widgetId = this.config.widgetId;
      this.parentCtrl = config.parentCtrl;
    },
    sendWidgetEvent: function sendWidgetEvent(params) {
      params.widgetId = this.widgetId;
      if (
        typeof personetics !== 'undefined' &&
        personetics !== null &&
        Object.prototype.hasOwnProperty.call(personetics, 'sendWidgetEvent')
      )
        personetics.sendWidgetEvent(this, params);
      else Personetics.log('personetics JS API is not available');
    },
    getTemplateHtml: function getTemplateHtml(templateId, ctxObj) {
      var tmpl = this.getTemplate(templateId);
      var html = tmpl(ctxObj);
      return html;
    },
    getTemplate: function getTemplate(templateId) {
      var template = this.getPrecompiledTemplate(templateId);

      if (!template) {
        var errorObject = new Error("Unknown template '" + templateId + "'");
        Personetics.error(errorObject.message, false, errorObject);
      }

      return template;
    },
    getPrecompiledTemplate: function getPrecompiledTemplate(templateId) {
      var id = templateId;
      if (id.substr(0, 1) == '#') id = id.substr(1);
      var tmpl = Handlebars.templates[id];
      return tmpl;
    }
  });
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $) {
  Personetics.UI = Personetics.UI || {};
  var factory = Base.extend({
    widgetFactoryMap: {},
    counter: 0,
    registerWidget: function registerWidget(type, config, name) {
      // backwards compatibility
      if (typeof name !== 'undefined') this.registerWidgetDeprecated(type, config, name);
      else {
        var entry = this.widgetFactoryMap[type];

        if (!entry) {
          entry = {
            type: type,
            // widget type
            name: config.widgetName,
            widthModes: {} // additional widgets based on screen width
          };
          this.widgetFactoryMap[type] = entry;
        } // map widget by width mode

        if (config.widthMode) {
          entry.widthModes[config.widthMode] = config.widgetClass;
        } // default block mapping
        else {
          entry.widgetClass = config.widgetClass;
        }
      }
    },
    createWidget: function createWidget(type, config) {
      var entry = this.widgetFactoryMap[type];
      var widgetClass = null;

      if (entry) {
        if (entry['class']) {
          return this.createWidgetDeprecated(type, config);
        } else {
          if (
            Object.prototype.hasOwnProperty.call(config, 'widthMode') &&
            typeof config.widthMode !== 'undefined' &&
            config.widthMode !== null &&
            config.widthMode.length > 0
          ) {
            widgetClass = entry.widthModes[config.widthMode];
          }

          if (!widgetClass) widgetClass = entry.widgetClass;
        }
      }

      if (widgetClass) return new widgetClass(config);
      else
        throw "Personetics.UI.PWidgetFactory.createWidget() - Invalid widget type '" + type + "'";
    },
    registerWidgetDeprecated: function registerWidgetDeprecated(type, clazz, name) {
      if (type && type.length > 0 && clazz) {
        this.widgetFactoryMap[type] = {
          class: clazz,
          name: name || type
        };
      }
    },
    createWidgetDeprecated: function createWidgetDeprecated(type, config) {
      if (Object.prototype.hasOwnProperty.call(this.widgetFactoryMap, type)) {
        var clazz = this.widgetFactoryMap[type]['class'];
        var widgetId = type + '_' + Date.now() + '_' + this.counter;
        this.counter++;
        config.widgetId = widgetId;
        return new clazz(config);
      } else {
        throw "Personetics.UI.PWidgetFactory.createWidget() - Invalid widget type '" + type + "'";
      }
    }
  });
  Personetics.UI.PWidgetFactory = new factory();
})(window, (window.Personetics = window.Personetics || {}), jQuery);

(function (window, Personetics, $) {
  Personetics.API = Personetics.API || {};
  Personetics.API.personetics = new (function () {
    this.isInitialized = false;
    this.pserverProxyClassName = null;
    this.config = {
      protocolVersion: Personetics.projectConfiguration.getConfig('protocolVersion'),
      enableNotifyEvents: Personetics.projectConfiguration.getConfig('enableNotifyEvents')
    };
    this.urls = {
      INSIGHTS_SERVLET: '/insights/',
      STORY_SERVLET: '/story/',
      EXECUTE_SERVLET: '/execute',
      SERVICE_SERVLET: '/service/',
      INIT_PERSONETICS: 'initPersonetics',
      GET_INSIGHTS: 'getInsights',
      GET_INBOX_INSIGHTS: 'getInboxInsights',
      GENERATE_INSIGHTS: 'generateInsights',
      GET_INSIGHT_RATINGS: 'getInsightRating',
      UPDATE_INSIGHT_RATINGS: 'updateInsightRating',
      UPDATE_INSIGHT_FEEDBACK: 'updateInsightFeedback',
      UPDATE_USER_DATA_ASSETS: 'updateUserDataAssets',
      GET_INSIGHT_STORY: 'getInsightStory',
      GET_INSIGHT_DETAILS: 'getInsightDetails',
      GET_INSIGHT_STORY_DATA: 'getInsightStoryData',
      GET_INSIGHT_STORY_DEFINITION: 'getInsightStoryDefinition',
      GET_INSIGHT_STORY_TEXTS: 'getInsightStoryTexts',
      GET_STORY_QUERY_DATA: 'getStoryQueryData',
      GET_MESSAGE_HISTORY: 'messageHistory',
      CALL_PERSONETICS_API: 'callPersoneticsAPI',
      GET_BUDGETS_CATEGORIES: 'getBudgetsCategories',
      SET_BUDGETS_CATEGORIES: 'setBudgetSettings'
    };
    /**
     * personetics.initPersonetics
     * @param config
     * @param onSuccess
     * @param onFailure
     */

    this.initialize = function (config, onSuccess, onFailure) {
      window.skinSetup = new SkinSetup();
      window.skinSetup.init();
      this.config = $.extend(true, this.config, config);
      this.config.requestHeaders = this.config.requestHeaders || {};
      this.config.requestHeaders['authToken'] = this.config.userId; // only initialize once

      if (!this.isInitialized) {
        Personetics.utils.PLogger.setDebugMode(config.debugMode);
        Personetics.log('personetics.initialize() called'); // PServer request manager

        var pserverProxyClassName = this.pserverProxyClassName;

        if (pserverProxyClassName) {
          this.pserverProxy = new pserverProxyClassName();
        } else {
          this.pserverProxy = new Personetics.API.PServerProxyNative();
        }

        this.pserverProxy.start(this.config); // Events delegate - use default if not provided by customer

        if (
          Object.prototype.hasOwnProperty.call(this.config, 'pDelegate') &&
          this.config.pDelegate
        ) {
          this.eventsDelegate = this.config.pDelegate;
        } else {
          this.eventsDelegate = new EventDelegate();
        } // Notify events manager

        this.eventBuffer = new Personetics.utils.PEventsBuffer();
        this.eventBuffer.start(); // Skin

        this.skinController = new Personetics.story.skinController();
        this.skinController.registerWidgets(); // Do initPersonetics call or invoke success callback

        if (
          Object.prototype.hasOwnProperty.call(this.config, 'callInitPersonetics') &&
          this.config.callInitPersonetics == true
        ) {
          this.initPersonetics(
            function (response) {
              this.isInitialized = true;
              onSuccess(response);
            }.bind(this),
            onFailure
          );
        } else {
          this.isInitialized = true;
          if (onSuccess) onSuccess();
        }
      } else {
        onSuccess();
      }
    };
    /**
     * personetics.notifyEvent
     * @param eventObj
     * @param onSuccess
     * @param onFailure
     */

    this.notifyEvent = function (eventObj, onSuccess, onFailure) {
      if (
        this.eventBuffer &&
        ((Object.prototype.hasOwnProperty.call(this.config, 'enableNotifyEvents') &&
          this.config.enableNotifyEvents == true) ||
          (Object.prototype.hasOwnProperty.call(eventObj.eventMessage.eventInfo, 'type') &&
            eventObj.eventMessage.eventInfo.type === 'Navigation'))
      ) {
        this.eventBuffer.addEventToSend(eventObj, onSuccess, onFailure);
      }
    };
    /**
     * personetics.startWidget
     * @param config
     */

    this.startWidget = function (config) {
      if (typeof config === 'undefined' || config === null) {
        var errorObject = new Error(
          'personetics.startWidget Error: invalid widget configuration object'
        );
        var errorMessage =
          'Error Message: ' + errorObject.message + ' Error Stack:' + errorObject.stack;
        Personetics.error(errorMessage, false, errorObject);
      }

      window.skinSetup = window.skinSetup || new SkinSetup();

      if (window.skinSetup) {
        var filter = config.filter;
        if (typeof filter === 'undefined' || filter === null)
          config.filter = config.widgetType != 'teaser-widget-carousel' ? true : false;
      }

      var el = $('body');

      if (Object.prototype.hasOwnProperty.call(config, 'el')) {
        if (!config.el.jquery) {
          config.el = $(config.el);
        }

        el = config.el;
      }

      var initPersoneticsConfig = $.extend(true, {}, config);
      if (
        typeof EventDelegate !== 'undefined' &&
        EventDelegate !== null &&
        $.isFunction(EventDelegate)
      )
        initPersoneticsConfig.eventsDelegate = new EventDelegate();
      initPersoneticsConfig.base64EncodeResponse = false;
      initPersoneticsConfig.requestHeaders = initPersoneticsConfig.requestHeaders || {};
      initPersoneticsConfig.requestHeaders['authToken'] = config.userId;

      var onError = function onError() {
        Personetics.log('Failed to call initPersonetics');
        var firstWidget = Personetics.UI.PWidgetFactory.createWidget('master', config);
        firstWidget.start(el);
      };

      var onPersoneticsInitSuccess = function onPersoneticsInitSuccess(response) {
        if (response !== undefined) {
          if (
            Object.prototype.hasOwnProperty.call(response, 'projectConfiguration') &&
            response.projectConfiguration !== undefined
          )
            Personetics.projectConfiguration.setConfig(response.projectConfiguration);
        }

        var widgetType = 'master';
        var firstWidget = Personetics.UI.PWidgetFactory.createWidget(
          widgetType,
          initPersoneticsConfig
        );
        firstWidget.start(el);
      };

      personetics.initialize(initPersoneticsConfig, onPersoneticsInitSuccess, onError);
    };

    this.initPersonetics = function initPersonetics(onSuccess, onFailure) {
      var type = this.urls.INIT_PERSONETICS;
      var data = {
        type: type,
        mode: this.config.mode,
        predefinedFile: this.config.predefinedFile,
        isPredefined: this.config.isPredefined
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };

    this.getConfig = function getConfig() {
      return this.config;
    }; // this.setConfig = function setConfig(apiConfig){
    //     Personetics.projectConfiguration.setConfig(apiConfig);
    // };

    /**
     * personetics.getInsights
     * @param bankInfo
     * @param onSuccess
     * @param onFailure
     */

    this.getInsights = function getInsights(bankInfo, onSuccess, onFailure, autoGenerate) {
      var type = this.urls.GET_INSIGHTS;
      var data = {
        type: type,
        bankInfo: bankInfo
      };

      if (typeof autoGenerate !== 'undefined' && autoGenerate != null) {
        data.autoGenerate = autoGenerate;
      }

      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };

    this.callPersoneticsAPI = function (apiName, apiBody, successCallback, failureCallback) {
      if (apiName && apiBody) {
        var _apiBody = getApiConfigs(apiBody, this.config);

        var data = $.extend({}, _apiBody, true);

        var defaultOnSuccess = function defaultOnSuccess(res) {
          Personetics.log('CallPersoneticsAPI success: ' + res);
        };

        var defaultOnFailure = function defaultOnFailure(e) {
          Personetics.log('CallPersoneticsAPI failed: ' + e.message);
        };

        var onSuccess =
          successCallback && $.isFunction(successCallback) ? successCallback : defaultOnSuccess;
        var onFailure =
          failureCallback && $.isFunction(failureCallback) ? failureCallback : defaultOnFailure;
        data.type = apiName;
        this.pserverProxy.getData(
          {
            postData: data
          },
          onSuccess,
          onFailure,
          true
        );
      } else {
        throw 'apiName or apiBody is not defined';
      }
    };

    var getApiConfigs = function getApiConfigs(obj, config) {
      var apiConfigsObj = $.extend({}, obj, true);

      if (apiConfigsObj && Object.keys(apiConfigsObj).length) {
        for (var key in apiConfigsObj) {
          if (_typeof(apiConfigsObj[key]) == 'object' && !Array.isArray(apiConfigsObj[key])) {
            apiConfigsObj[key] = getApiConfigs(apiConfigsObj[key], config);
          } else if (Object.prototype.hasOwnProperty.call(config, key)) {
            apiConfigsObj[key] = config[key];
          }
        }
      }

      return apiConfigsObj;
    };
    /**
     * personetics.getInboxInsights
     * @param bankInfo
     * @param onSuccess
     * @param onFailure
     */

    this.getInboxInsights = function getInbox(bankInfo, onSuccess, onFailure) {
      var type = this.urls.GET_INBOX_INSIGHTS;
      var data = {
        type: type,
        bankInfo: bankInfo
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };

    this.getInsightStory = function getInsightStory(insightData, onSuccess, onFailure) {
      var type = this.urls.GET_INSIGHT_STORY;
      var servletName = this.urls.STORY_SERVLET;
      if (!this.config.useServiceServlet) insightData['servletName'] = servletName;

      if (
        Object.prototype.hasOwnProperty.call(insightData, 'id') &&
        Object.prototype.hasOwnProperty.call(insightData, 'insightId')
      ) {
        insightData['type'] = type;
        var options = {
          postData: insightData
        };
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightStory(): Usage: getInsightStory(id, insightId)';
      }
    };

    this.getInsightDetails = function getInsightDetails(insightData, onSuccess, onFailure) {
      if (Object.prototype.hasOwnProperty.call(insightData, 'insightId')) {
        var type = this.urls.GET_INSIGHT_DETAILS;
        insightData['type'] = type;
        var options = {
          postData: insightData
        };
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightDetails(): Usage: getInsightDetails(userId, insightId)';
      }
    };

    this.getInsightStoryData = function getInsightStoryData(insightData, onSuccess, onFailure) {
      var type = this.urls.GET_INSIGHT_STORY_DATA;
      var data = {
        type: type
      };
      var options = {
        postData: data
      };

      if (
        Object.prototype.hasOwnProperty.call(insightData, 'id') &&
        Object.prototype.hasOwnProperty.call(insightData, 'insightId')
      ) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightStoryData(): Usage: getInsightStoryData(id, insightId)';
      }
    };

    this.getInsightStoryDefinition = function getInsightStoryDefinition(
      insightData,
      onSuccess,
      onFailure
    ) {
      var type = this.urls.GET_INSIGHT_STORY_DEFINITION;
      var data = {
        type: type
      };
      var options = {
        postData: data
      };

      if (
        Object.prototype.hasOwnProperty.call(insightData, 'id') &&
        Object.prototype.hasOwnProperty.call(insightData, 'insightId')
      ) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightStoryDefinition(): Usage: getInsightStoryDefinition(id, insightId)';
      }
    };

    this.getInsightStoryTexts = function getInsightStoryTexts(insightData, onSuccess, onFailure) {
      var type = this.urls.GET_INSIGHT_STORY_TEXTS;
      var data = {
        type: type
      };
      var options = {
        postData: data
      };

      if (
        Object.prototype.hasOwnProperty.call(insightData, 'id') &&
        Object.prototype.hasOwnProperty.call(insightData, 'insightId')
      ) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightStoryTexts(): Usage: getInsightStoryTexts(id, insightId)';
      }
    };

    this.getInsightMessages = function getInsightMessages(data, onSuccess, onFailure) {
      var type = this.urls.GET_MESSAGE_HISTORY;
      var options = {
        postData: {
          type: type
        }
      };
      this.pserverProxy.getData(options, type, onSuccess, onFailure);
    };

    this.generateInsights = function generateInsights(bankInfo, onSuccess, onFailure) {
      var type = this.urls.GENERATE_INSIGHTS;
      var data = {
        bankInfo: bankInfo
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, type, onSuccess, onFailure);
    };

    this.getInsightRating = function getInsightRating(data, onSuccess, onFailure) {
      var type = this.urls.GET_INSIGHT_RATINGS;
      data.type = type;
      var options = {
        postData: data
      };

      if (Object.prototype.hasOwnProperty.call(data, 'id')) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getInsightRating(): Usage: getInsightRating(id)';
      }
    };

    this.updateInsightRating = function updateInsightRating(data, onSuccess, onFailure) {
      var type = this.urls.UPDATE_INSIGHT_RATINGS;
      data.type = type;
      var options = {
        postData: data
      };

      if (
        Object.prototype.hasOwnProperty.call(data, 'id') &&
        Object.prototype.hasOwnProperty.call(data, 'rating')
      ) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.updateInsightRating(): Usage: updateInsightRating(id, rating)';
      }
    };

    this.updateUserDataAssets = function updateUserDataAssets(eventObj, onSuccess, onFailure) {
      var type = this.urls.UPDATE_USER_DATA_ASSETS;
      var data = {
        type: type,
        eventList: eventObj
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };

    this.updateInsightFeedback = function updateInsightFeedback(data, onSuccess, onFailure) {
      var type = this.urls.UPDATE_INSIGHT_FEEDBACK;
      data.type = type;
      var options = {
        postData: data
      };

      if (Object.prototype.hasOwnProperty.call(data, 'id')) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.updateInsightFeedback(): Usage: updateInsightFeedback(id, rating)';
      }
    };

    this.getQueryData = function (data, onSuccess, onFailure) {
      var type = this.urls.GET_STORY_QUERY_DATA;
      var servletName = this.urls.STORY_SERVLET;
      data.type = type;
      if (!this.config.useServiceServlet) data.servletName = servletName;
      var options = {
        postData: data
      };

      if (
        Object.prototype.hasOwnProperty.call(data, 'queryName') &&
        Object.prototype.hasOwnProperty.call(data, 'queryParams')
      ) {
        this.pserverProxy.getData(options, onSuccess, onFailure);
      } else {
        throw 'personetics.getQueryData(): Usage: getQueryData(queryName, queryParams)';
      }
    };

    this.handlePServerResponse = function (data, requestId) {
      if (
        Object.prototype.hasOwnProperty.call(this.pserverProxy, 'handlePServerResponse') &&
        $.isFunction(this.pserverProxy.handlePServerResponse)
      )
        this.pserverProxy.handlePServerResponse(data, requestId);
    };

    this.sendWidgetEvent = function sendWidgetEvent(widget, eventName, params) {
      var bridge = this.getJSBridge();

      if (bridge) {
        bridge.widgetEvent(widget, eventName, params);
      }
    };

    this.getJSBridge = function () {
      return this.eventsDelegate;
    };

    this.getSkinController = function () {
      return this.skinController;
    };

    this.getDeviceType = function () {
      return this.config.deviceType;
    };

    this.getProjectConfigValue = function getProjectConfigValue(configId) {
      return Personetics.projectConfiguration.getConfig(configId);
    };

    this.setProjectConfiguration = function setProjectConfiguration(apiConfig) {
      Personetics.projectConfiguration.setConfig(apiConfig);
    };

    this.getStoryVariableMapping = function getStoryVariableMapping() {
      var manager = window.PBlockProcessorManager.getInstance();
      return manager.model ? manager.model.getDlgVarMapping() : [];
    };
    /**
     * personetics.getBudgetsCategories
     * @param onSuccess
     * @param onFailure
     */

    this.getBudgetsCategories = function getBudgetsCategories(onSuccess, onFailure) {
      var type = this.urls.GET_BUDGETS_CATEGORIES;
      var data = {
        type: type
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };
    /**
     * personetics.setBudgetSettings
     * @param settings
     * @param onSuccess
     * @param onFailure
     */

    this.setBudgetSettings = function setBudgetSettings(settings, onSuccess, onFailure) {
      var type = this.urls.SET_BUDGETS_CATEGORIES;
      var data = {
        type: type,
        settings: settings
      };
      var options = {
        postData: data
      };
      this.pserverProxy.getData(options, onSuccess, onFailure);
    };
  })();
  window.personetics = Personetics.API.personetics;
})(window, (window.Personetics = window.Personetics || {}), jQuery);
