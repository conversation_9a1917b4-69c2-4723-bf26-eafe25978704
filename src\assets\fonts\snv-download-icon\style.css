@font-face {
  font-family: 'snv-download-icon';
  src:  url('fonts/snv-download-icon.eot?zigyhh');
  src:  url('fonts/snv-download-icon.eot?zigyhh#iefix') format('embedded-opentype'),
    url('fonts/snv-download-icon.ttf?zigyhh') format('truetype'),
    url('fonts/snv-download-icon.woff?zigyhh') format('woff'),
    url('fonts/snv-download-icon.svg?zigyhh#snv-download-icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="snv-"], [class*=" snv-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'snv-download-icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.snv-snv-download:before {
  content: "\e900";
}
