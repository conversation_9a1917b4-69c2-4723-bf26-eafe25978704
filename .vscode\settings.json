{"vsicons.presets.angular": false, "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[xml]": {"editor.defaultFormatter": "redhat.vscode-xml"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[less]": {"editor.defaultFormatter": "vscode.css-language-features"}, "less.format.spaceAroundSelectorSeparator": true, "scss.format.spaceAroundSelectorSeparator": true, "[typescript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}