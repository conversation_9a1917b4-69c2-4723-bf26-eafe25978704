// Proxy used by Synvous Network
process.env.proxyServer = `http://proxy.snv.net:8080`;
process.env.HTTPS_PROXY = process.env.proxyServer;
process.env.HTTP_PROXY = process.env.proxyServer;

// This proxyHost is different from the two settings (proxyDomain, proxyServer) above:
//    +----------------------------------------+
//    |Synovus Proxy Server                    |
//    | +-----------------------------------+  |
//    | |Application Proxy Server           |  |
//    | | +------------------------------+  |  |
//    | | |App-Enhancements(Extensions)  |  |  |
//    | | |                              |  |  |
//    | | |                              |  |  |
//    | | +------------------------------+  |  |
//    | +-----------------------------------+  |
//    +----------------------------------------+

process.env.proxyHost = `snvint2.d3hosted.com`;
process.env.proxyTarget = `https://${process.env.proxyHost}`;

process.env.extensionPath = `/extensions/`;
