import { getBaseUrl, isMobileApp } from '@d3banking/device-utils';
import { openUrl } from '@d3banking/domain-utils/lib/utils/links';
import { t } from '@d3banking/l10n/lib/util/t';
import { Box } from '@mui/material';
import { createRoot } from 'react-dom/client';
import { ExternalRedirect } from '@d3banking/banking-components/lib/ExternalRedirect';

import { cacheBuster, getAccountId } from '../utils';
import { RenderWithBundle } from '../../common/RenderWithBundle';
import { getSAMLData } from '../ssoRedirectUtils';
import { ssoDeL10n } from '../l10n';


// export function showSpeedBump(url: string) {
//   const modalContainer = document.createElement('div');
//   document.body.appendChild(modalContainer);
//   const root = createRoot(modalContainer);
//   root.render(
//     <ExternalRedirect
//       text={t(
//         'ui-synovus:accounts.redirect.message',
//         `You’ve clicked a link that will take you outside Synovus.com. There, you’ll be able to purchase other financial products which may not be covered by FDIC insurance and may lose value. Still want to continue?`
//       )}
//       title={t('ui-synovus:accounts.redirect.title', `You're about to leave Synovus`)}
//       redirectUrl={url}
//       errorTitle=""
//       errorText=""
//       cancelText="Go Back"
//       confirmText="Continue"
//       timerInSeconds={30}
//     />
//   );
// }




const showSsoRedirect = () => {
  const modalContainer = document.createElement('div');
  document.body.appendChild(modalContainer);
  const root = createRoot(modalContainer);
  
  const getRedirectUrl = async () => {
    const path = `/d3rest/ssodehtml/${getAccountId()}?${cacheBuster()}`;
    if (isMobileApp()) {
      const baseUrl = await getBaseUrl();
      return `${baseUrl}${path}`;
    }
    return path;
  };

  getRedirectUrl().then(url => {
    root.render(
      <ExternalRedirect
        text={t(
          'ui-synovus:accounts.sso.message',
          `You're about to access your account management portal. Continue?`
        )}
        title={t('ui-synovus:accounts.sso.title', `Account Management Portal`)}
        redirectUrl={url}
        cancelText="Go Back"
        confirmText="Continue"
        timerInSeconds={30}
        export function showSpeedBump(url: string) {
  const modalContainer = document.createElement('div');
  document.body.appendChild(modalContainer);
  const root = createRoot(modalContainer);
  root.render(
    <ExternalRedirect
      text={t(
        'ui-synovus:accounts.redirect.message',
        `You’ve clicked a link that will take you outside Synovus.com. There, you’ll be able to purchase other financial products which may not be covered by FDIC insurance and may lose value. Still want to continue?`
      )}
      title={t('ui-synovus:accounts.redirect.title', `You're about to leave Synovus`)}
      redirectUrl={url}
      errorTitle=""
      errorText=""
      cancelText="Go Back"
      confirmText="Continue"
      timerInSeconds={30}
    />
  );
}

      />
    );
  });
};

export const SsoButton = () => {
  const clickHandler = () => {
    showSsoRedirect();
  };

  return (
    <RenderWithBundle bundle="ui-synovus">
      <Box justifyContent="center" display="flex">
        <Box
          sx={{ width: '100%' }}
          component="button"
          type="submit"
          className="sso-link _secondary-button"
          onClick={clickHandler}
        >
          {t(ssoDeL10n.ssoDeButtonText, 'Manage Account')}
        </Box>
      </Box>
    </RenderWithBundle>
  );
};

