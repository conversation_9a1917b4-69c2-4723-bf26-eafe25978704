> [!NOTE]
> Please Follow UI Contributing Guidelines detailed here https://ui-docs.d3vcloud.com/contributing/guidelines

## Basics

| Key         | Value       |
| ----------- | ----------- |
| Link to Issue/Story (Jira/Salesforce) | #VALUE |
| PR Type (Fix or Enhancement) | #VALUE |
| Planned Due/Merge Date | #VALUE |
| Internal Reviewer | #VALUE |

## Checklist

- [ ] Documentation
- [ ] Tests
- [ ] Semantic classes on elements
- [ ] API Mocks

## Description

- For Defects:
  - how do we reproduce the error? (steps)
  - Expected vs Actual Results
- For New Extensions/Enhancements
  - Include relevant information not needed in the documentation
  - Link to documentation
  - List of new L10ns / Company Attributes used

## Screenshots

<table>
    <tr>
        <td>Mobile Before</td>
        <td>Mobile After</td>
    </tr>
    <tr>
        <td><img src="" alt="Mobile Before image"></td>
        <td><img src="" alt="Mobile After image"></td>
    </tr>
</table>

<table>
    <tr>
        <td>Desktop Before</td>
        <td>Desktop After</td>
    </tr>
    <tr>
        <td><img src="" alt="Desktop Before image"></td>
        <td><img src="" alt="Desktop image"></td>
    </tr>
</table>

