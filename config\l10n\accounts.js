const { i10GeneralExport } = require('../i10-general-export');

/**
 * DPD-42987: Temporary workaround to connect locally defined custom l10ns from mobile application with Control UI.
 * Defining l10ns here will include them into the publishing process.
 * Once the proper sync is established, definitions will be moved.
 */

const bundle = 'ui-synovus';

const requiredFieldDefinition = {
  'ext.dashboard.disclosures.button': {
    value: 'View Disclosures',
    description:
      'The label of the button displayed at the bottom of the account list.',
    version: '4.18'
  },
  'ext.dashboard.disclosures.header': {
    value: 'Disclosures',
    description:
      'The title of the bottom sheet where the disclosures are displayed.',
    version: '4.18'
  },
  'ext.dashboard.disclosures.1': {
    value: 'Some accounts listed on this website are deposit accounts related to investment accounts. Investment accounts are also displayed. Investment products and services are offered through Synovus Securities, Inc., member FINRA/SIPC. Investment products and services are not FDIC insured, are not deposits of or other obligations of Synovus Bank, __are not guaranteed by Synovus Bank and involve investment risk, including possible loss of principal amount invested__.',
    description:
      'The first unreferenced disclosure to be displayed on the Accounts Overview screen.',
    contentType: 'md',
    version: '4.18',
    updatedVersion: '5.3',
  },
  'ext.dashboard.disclosures.2': {
    value: 'Synovus Securities, Inc. is a subsidiary of Synovus Financial Corp and an affiliate of Synovus Bank',
    description:
      'The second unreferenced disclosure to be displayed on the Accounts Overview screen.',
    version: '4.18',
    updatedVersion: '5.3',
  },
  'ext.dashboard.disclosures.3': {
    value: 'For loan accounts displayed, the current loan balance may not reflect your current payoff amount. Please contact us to verify this amount',
    description:
      'The second unreferenced disclosure to be displayed on the Accounts Overview screen.',
    contentType: 'md',
    version: '4.18',
    updatedVersion: '5.3',
  },
  'ext.dashboard.disclosures.ref.1': {
    value: `{"sourceProductIds": ["SEC"], "disclosure": "Investment account balances reflect their values after market close (4pm EST) on the previous day and will be updated again after market close (4pm EST) today."}`,
    description:
      'The first referenced disclosure to be displayed on the Accounts Overview screen.',
    version: '4.18',
    updatedVersion: '5.3',
  },
  'ext.dashboard.disclosures.ref.2': {
    value: `{"sourceProductIds": ["LN200M", "LN210M", "LN290M", "LN291M", "LN570M", "LN572M", "LN575M"], "disclosure": "Mortgage balances reflect their values at close of business on the previous day. For current values and payoff information, please contact Customer Care."}`,
    description:
      'The second unreferenced disclosure to be displayed on the Accounts Overview screen',
    version: '4.18',
    updatedVersion: '5.3',
  },
  'greeting.name': {
    value: '{{firstName}}',
    description:
      'Account list banner "name" of the user (first name is set by default, but works with last as well).',
    substitutionParams: ['firstName', 'lastName'],
    version: '5.1',
    updatedVersion: '5.5',
  },
  'other-account.text': {
    value: 'May not reflect latest transactions',
    description:
      'Section subtitle for offline sources.',
    version: '5.1',
  }
};

module.exports = i10GeneralExport(requiredFieldDefinition, bundle);
