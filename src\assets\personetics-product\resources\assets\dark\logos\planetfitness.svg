<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 24.2.3, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1"
	 id="svg2" xmlns:cc="http://creativecommons.org/ns#" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 200 200"
	 style="enable-background:new 0 0 200 200;" xml:space="preserve">
<style type="text/css">
	.st0{fill:#111214;}
	.st1{fill:#FEFEFE;}
	.st2{fill:#FBFBFB;}
	.st3{fill:#EAE33B;}
	.st4{fill:#9D2987;}
	.st5{fill:#9C2886;}
	.st6{fill:#E9E23C;}
	.st7{fill:#D9D43B;}
	.st8{fill:#9A2B85;}
	.st9{fill:#D1CC3B;}
	.st10{fill:#B3B144;}
	.st11{fill:#8A2576;}
	.st12{fill:#626238;}
	.st13{fill:#747641;}
	.st14{fill:#8D2679;}
	.st15{fill:#BBB83C;}
	.st16{fill:#98993A;}
	.st17{fill:#454629;}
	.st18{fill:#363A22;}
	.st19{fill:#8F9037;}
	.st20{fill:#C1BF57;}
	.st21{fill:#AFAE54;}
	.st22{fill:#787938;}
	.st23{fill:#BCB83E;}
	.st24{fill:#333429;}
	.st25{fill:#595C2A;}
	.st26{fill:#702161;}
	.st27{fill:#595B33;}
	.st28{fill:#61632D;}
	.st29{fill:#F6F5F5;}
	.st30{fill:#191A1C;}
</style>
<g>
	<path class="st0" d="M173.9,117c0,0.3,0,0.7,0,1c-0.9,1-0.6,2.2-0.5,3.3c0.6,5.6-0.8,9.6-7,11.3c-3.9,1.1-4.1,4.3-2.2,7.7
		c2.5,4.4,2.6,8.7,0,13.2c-2.1,3.6-4.8,5.2-9,4.8c-6.2-0.6-6.7,0-6.8,6.5c0,4.5-0.8,8.5-5,11.3c-3.2,2.2-6.1,2.7-9.7,1.1
		c-6.9-3-8.5-2.1-10.5,5.2c-0.7,2.8-2.2,4.1-5.1,4.6c-2.1,0.4-4.3-0.2-6.3,0.9c-1,0-2,0-3,0c-2-1-3-3-4.4-4.6c-3-3.3-6.8-3.4-9,0.4
		c-1.6,2.9-3.8,3.7-6.6,4.2c-1,0-2,0-3,0c-5.6-0.6-9-3.2-9.9-9.2c-0.6-3.9-3.7-4.8-8.3-2.2c-3.9,2.2-7,1.7-10.6-0.7
		c-4.1-2.7-5.9-5.7-5.1-10.8c0.9-5.8-1.1-7.5-6.9-7.3c-8.2,0.3-13.2-6.9-10.1-14.3c0.6-1.4,1.3-2.7,1.7-4.1c1.1-3.6,0.2-6.2-3.6-7.4
		c-3.1-1-6.2-2-6.2-6.1c0-4.9-2.3-10.2,4.3-13.3c4.9-2.3,4.9-4.9,0.9-8.8c-6.4-6.4-4-16,4.7-18.6c6.6-2,7.2-3.1,5.6-9.9
		c-0.9-3.9-1.7-7.7,3.2-9.6c0.8,0,1.4,0.4,1.9,1.1c0.6,3.4,4.3,5.4,4.4,9.1c0,1.2-0.6,2.3-0.7,3.5c-0.8,3.2-0.3,5.9,2.9,7.6
		c1,0.5,1.9,1.4,2.1,2.6c-1.8,5.5-5,10.6-5.4,17.6c1.8-6.1,3.3-11.3,5.6-16.2c2-1.4,5.5-0.3,6.6-2.9c2-4.7,3.1-9.8,7.3-13.4
		c0.4-0.3,0.4-1.5,0.1-1.9c-3.9-4.5-0.2-7.2,2.3-10.4c3.4-4.3,3.2-4.6-2.3-6.4c-4.9-1.6-5.3-2.8-1.7-6.8c2.8-3.2,5.9-5.9,10.6-5.1
		c2,0.7,3.7,0.6,4.8-1.6c0.5-0.8,1.1-1.5,1.9-2c5-1.8,5.2-6.2,5.6-10.4c0.2-2-0.6-4.3,1.2-6c2.7,0.1,3.1,2.4,4,4.2
		c0.7,4.1,1.3,8.1,2.3,12.1c0.4,2.7,0,4.7-2.7,6.8c-2.1,1.5-2.6,5.1-2.8,8c-0.1,1.7,0.1,3.3-0.6,4.9c-1.1,1.8-0.5,3.1,1.1,4.2
		c0.5,0.4,0.9,0.9,1.4,1.3c2.6,1.7,2.6-2.2,4.5-2c0.8-0.1,1.4,0.1,1.9,0.7c4.1,3.1,9.4,3,13.8,5.3c12.8,6.5,23.3,14.9,27.7,29.5
		c2.6,8.7,3.1,17.2,1.3,26c-0.6,3-3.1,5.2-3,8.3c-0.1,0.9-0.5,1.7-0.7,2.6c-0.2,0.6,0.1,1.1,0,0.4c-0.1-0.7,0.6,0.2,0-0.2
		c-0.5-0.4,0.6-1,0.1-0.5c4-8.4,5.6-17.3,5.3-26.5c-0.2-7.8-3.6-14.4-7.4-21.2c-4.3-7.9-10.1-13.4-17.9-17.1
		c-4.8-2.3-9.7-4.2-14.9-5.2c-1.8-0.4-3.7-0.2-4.8-2c-0.1-6.7-1.3-13.3-1.6-19.9c1.2-3.8,3.5-3.3,6.1-1.6c2,1.3,3,3.5,4.4,5.3
		c2.6,3.6,4.8,3.8,8.3,1.1c7.9-6,15.7-2.8,17.7,7.2c1.1,5.7,2.8,6.8,8.6,5.8c8.9-1.6,15.4,6.1,12.5,14.6c-1.8,5.3-1.1,6.7,4.5,8.1
		c4.6,1.2,7.6,3.6,9.1,8.2c1.2,3.9,0.7,7-2.1,10c-5,5.4-4.7,6.9,1.5,10.4C172.4,113.9,173.2,115.3,173.9,117z"/>
	<path class="st1" d="M173.9,117c-1.4-1.4-2.6-3-4.8-3.2c-6.2-0.6-7.7-7.4-2.4-11.3c4.5-3.3,4.5-7,2.4-11.6
		c-1.7-3.8-4.7-4.7-8.3-5.3c-6-1-7.5-3.7-4.9-9.3c1.6-3.4,2.5-6.1-0.5-9.5c-3.1-3.4-6.3-5-10.9-3.8c-6.2,1.7-9.4-0.7-9.4-7.2
		c0-4.6-2.3-6.8-6.1-8.6c-4.1-1.9-6.7-0.6-9.6,2.4c-4.1,4.4-7,3.8-10.1-1.2c-1.2-2-2.6-3.9-4.1-5.6c-1.4-1.5-3.1-2.2-4.3,0.4
		C99,43.6,98.8,42,98.1,41c-2.1-4-2-8.4-2.3-12.7c-1-3.6-1.6-7.3-3.1-10.7c-1.1-1.9-2.6-3.1-4.8-2.4c-2.8,0.8-4.4,2.9-5,5.6
		c-0.7,3.6-0.7,7.2,0.1,10.8c0.7,2.1,1.4,4.3,2.7,6.2c0.5,1.1,0.6,2.2,0.3,3.4c-0.3,0.7-0.7,1.4-1,2.1c-1.2,0.6-2.4,0.3-3.6-0.1
		c-4.5-2.8-9.5-3.8-14.7-4.1c-6-0.3-7.9,1.3-8.6,7.2c-0.3,2.3-1.6,3.5-3.3,4.6c-6.1,3.8-6,3.9-4.4,10.3c0.1,2.9-1.6,4.3-4.2,4.9
		c-3.1,2.4-3.2,5.4-2,8.9c2.8,7.9,1.9,9.5-6,11.2c-4.7,1-6.6,4.3-8,8.5c-1.3,3.9,0.9,5.7,3.4,7.9c5.5,4.7,4.9,8.4-1.6,11.5
		c-2.7,1.3-5.5,2.6-4.5,6.1c1.1,3.8-1,9.1,6,10.2c6.9,1.1,8,5,4.1,11.8c-3.2,5.7-2.8,8.2,1.8,12.6c1.6,1.6,3,2.1,5.6,1.4
		c6.6-1.9,11.2,3.1,8.9,9.7c-1.1,3-1.2,5.4,1.6,6.7c3.3,1.5,5.7,6.8,10.7,3.1c1.4-1.1,3.2-1.8,4.9-2.4c3-1,6.4,0.7,6.3,3.1
		c-0.3,6.7,3.9,9.1,8.7,11.5c-20,0-39.9,0-59.9,0c0-57.7,0-115.4-0.1-173.1c0-2.4,0.5-2.8,2.8-2.8c47.4,0.1,94.8,0.1,142.2,0
		c2.8,0,2.9,0.9,2.9,3.2C173.9,49.1,173.9,83.1,173.9,117z"/>
	<path class="st1" d="M112,187.9c1.2-1.9,3.3-0.7,5-1.3c3.1-1.2,5.4-2.2,5.4-6.4c0-6.6,4.8-8.4,12-4.6c3.6,1.9,5.8,1.2,8.9-0.8
		c4.3-2.9,4.2-6.7,3.7-11c-0.6-6.1,1.6-8.1,7.8-7.2c2.6,0.4,5.3,1.4,7.1-1.6c2.1-3.5,5-6.9,2.5-11.4c-0.6-1-1.2-2-1.8-3
		c-2.8-4.6-1.6-8.9,3.9-9.6c6.1-0.8,6.7-4.1,6.5-8.9c-0.1-1.3-0.6-3,1.1-4c0,22.3,0,44.6,0.1,66.9c0,2.5-0.6,3.1-3.1,3.1
		C151.3,187.9,131.6,187.9,112,187.9z"/>
	<path class="st2" d="M89,187.9c2.1-1.2,3.9-2.4,5.2-4.8c2.7-5.1,8.7-5.1,12-0.4c1.1,1.6,1.8,3.4,2.8,5.2
		C102.3,187.9,95.7,187.9,89,187.9z"/>
	<path class="st3" d="M49.3,61.1c-3.7-6.8-3.4-7.6,3.8-11c2.4-1.1,4.7-1.9,3.3-5.8c-1.1-3,1.3-5.5,4.4-6.1
		c7.6-1.6,14.8-0.8,21.2,4.1c0.2,1.7,0.4,3.2-2.1,2.8c-6.4-0.8-9.1,4.1-12.7,8.5c2.9,0.7,5.4,1.2,7.9,1.9c1.6,0.5,3.7,1.5,2,3.4
		c-2.6,3-3.4,7.4-7.7,8.9c4.9,5.4,4.3,9-2.3,12.4c-0.8,0.4-2.4,0.9-1.3,1.9c4.6,4-1.1,5.4-2,7.8c-1.4,3.7-4.4,0.9-6.7,0.9
		c-0.7-0.2-0.9-0.9-1.2-1.4c-1-1.7-3.1-1.5-4.4-2.5c-2.4-1.9-2.4-4.8-1.5-7.6c0.1-1.2,2.5-1.7,1.1-3.4c0.3-1.8-0.9-2.4-2.2-3.3
		c-1.8-1.2-2.2-3.1-1.8-5.2C47,64.9,51.2,64,49.3,61.1z"/>
	<path class="st4" d="M140.8,133.8c-0.2-1.2-1.6-2.6-0.3-3.7c2.7-2.3,1.9-5.7,2.9-8.3c3.9-10.1-0.1-19.1-3.1-27.9
		c-2.1-6-5.8-11.9-12.8-14.4c-1.8-0.7-2.1-3.4-4.1-4.7c-5.6-3.5-11.6-5.9-18.1-7c-1.8-0.3-4.6-0.5-3.1-3.9l-0.1,0.1
		c13.7,0.2,26.2,5.5,34.1,15.7c13.1,17.1,16.9,36.7,5.8,57C139.4,136.6,140.8,135,140.8,133.8z"/>
	<path class="st5" d="M132.7,148.3c-0.6,2.9-1.6,4.5-4.4,5.2c-2.5,0.7-4.8,2.3-7.1,3.6c-8.1,4.4-16.4,6.1-25.9,5.5
		c-10-0.6-18-4.5-26.1-9.2c-3.5-2-4.9-5.6-7.4-8.4c-0.6-0.7-1.3-1.9-0.1-2.5c1.1-0.6,2.7-0.8,3.4,0.9c2.5,6.1,8,8.3,13.4,10.6
		c4.6,2,8.7,4.7,13.9,5.4c7.7,1,14.5-0.3,21.8-3.1C120.4,153.9,125.3,148.9,132.7,148.3z"/>
	<path class="st6" d="M86,41.1c-1.2-0.7-1-1.9-1.1-3.1c-0.4-2.5-0.6-5-3.2-6.3c-1.6-5.3-1.7-10.5,1.5-15.3c3.1-4.7,7.7-4.4,10.8,0.3
		c-1.5,2.4,2.3,5.3-0.8,7.5c0.1,4-0.2,7.9-1,11.9C91.4,40.1,89.7,41.5,86,41.1z"/>
	<path class="st7" d="M98.1,41c0.9,0.8,1.8,1.6,2.8,2.4c1.3,6.9,3,13.7,1.2,20.8c0,0,0-0.1,0.1-0.1c-0.6,0.1-1.2,0.1-1.8,0.2
		c-1.9,0.2-2.9,3.1-5.4,1.9c-0.4-0.3-0.8-0.7-1.1-1c-0.4-1.8-1.6-3.3-1.7-5.2c-0.2-1.5,1.1-2.9,0.1-4.6c-0.9-1.6,1-3.5,1-4.7
		C93.1,46.4,99.4,45.5,98.1,41z"/>
	<path class="st8" d="M56,89.3c0.8,0.1,1.2,0.7,1.2,1.4c0,6-4.2,10.9-4.3,17c0,1.7-1.6,1.3-2.7,1.2c-1.1-0.2-2.1-1.2-1.6-2.1
		C51.6,101.2,51.4,94.2,56,89.3z"/>
	<path class="st9" d="M122.8,99.1c-3.8-2.1-6.6-5.3-9.7-8.1l0,0.1c4.7-0.2,8.5,2.2,12.2,4.6C125.9,97.9,126.3,99.9,122.8,99.1z"/>
	<path class="st10" d="M105.8,89.9c-1.5,0.4-3.1-2.5-4.5,0.2c-0.5-0.3-1.3-0.4-1.6-0.9c-1.7-2.9-3.2-5.8-1.8-9.3
		c1.2-0.4,2.2,0.4,2.3,1.2c0.4,2.9,2.5,3.4,4.8,3.8l0.4-0.3C106.7,86.3,107.7,88,105.8,89.9z"/>
	<path class="st11" d="M54,133c-2.6-0.3-2.2-3.1-3.2-4.7c-0.3-0.5-0.1-3.2,1.7-2.8c2.3,0.5,2.7,3,3.3,5
		C56.1,131.8,55.7,132.8,54,133z"/>
	<path class="st12" d="M93.2,24.3c0.2-2-0.4-3.8-1.1-5.6c-0.7-1.8,0.6-1.8,1.8-1.9c2.2,3.6,1.6,7.6,1.9,11.5
		C94.2,27.4,95,25,93.2,24.3z"/>
	<path class="st13" d="M105,84.9c-5.4,2.7-4.9-3.1-7.1-5c0.3-0.5,0.6-1.1,0.8-1.6c3.1-0.3,3.6,1.5,3.3,4
		C102.2,84.1,104.8,83.2,105,84.9z"/>
	<path class="st14" d="M52.2,117.4c-0.3,1.2-0.2,3.1-2,2.8c-1.2-0.2-1.8-1.9-1.5-3.3c0.3-1.2,0.2-3.1,2.1-2.8
		C52.7,114.4,52.1,116.2,52.2,117.4z"/>
	<path class="st15" d="M102.1,82.3c-1-1.4-0.9-3.7-3.3-4c-1.6-1.4-0.6-2.6,0.6-4.4c0.9,2.7,2.6,4.5,4.2,6.3
		C106.1,82.9,102.7,81.7,102.1,82.3z"/>
	<path class="st16" d="M82.6,105.7c-2.8-1.7-3.8-4.3-4.9-6.8C80.7,99.9,82.6,101.8,82.6,105.7z"/>
	<path class="st17" d="M105.8,89.9c0.3-1.8-1.4-3.4-0.5-5.2c4.2-0.2,3.6,2.8,3.6,5.4l0-0.1C107.9,90,106.9,89.9,105.8,89.9z"/>
	<path class="st18" d="M109,90c1.7-1.1,3.2-1.2,4.1,1c0,0,0-0.1,0-0.1c-1.4,0.9-2.2,2.9-4.4,2.6C107.7,92.3,109.3,91.2,109,90
		C109,90,109,90,109,90z"/>
	<path class="st19" d="M122.8,99.1c2-0.3,2.3-1.9,2.5-3.5c2.2,1.1,3.1,2.7,1.1,4.6C124.7,101.9,123.4,101.4,122.8,99.1z"/>
	<path class="st20" d="M79.9,45.1c0.7-0.9,1.4-1.9,2.1-2.8c1.2-0.3,2.1,0.4,3,0.9C84.6,47.3,82.3,46.4,79.9,45.1z"/>
	<path class="st21" d="M95,66.2c1.9-0.3,3.1-2.5,5.4-1.9c-0.8,1-1.6,2.1-2.4,3.1C95.7,70.1,95.2,68.4,95,66.2z"/>
	<path class="st22" d="M92.1,60c1.3,1.5,3.3,2.8,1.7,5.2C91.5,64,88.7,63,92.1,60z"/>
	<path class="st23" d="M71.9,84.6c1.6-1.3,2.7-2.7,4.5-1.7c1,0.5,0.2,1.4-0.1,2.1C74.9,87.4,73.6,85.5,71.9,84.6z"/>
	<path class="st24" d="M49.3,61.1c3.5,3.4-0.8,4.3-2,6.3c-0.4-0.4-0.8-0.9-1.2-1.3C46.7,64.1,49.9,63.8,49.3,61.1z"/>
	<path class="st25" d="M51.3,75.8c1.7,0,3.1,0.6,4.1,2.5c-1.6,1.2-3.9-1.1-5.1,0.9C49.8,77.8,50.8,76.9,51.3,75.8z"/>
	<path class="st26" d="M140.8,133.8c0.3,1-0.1,2.3,1.1,3.1c-0.4,0.7-0.8,1.4-1.3,2c-0.5,0.7-1.2,1.2-2,0.6c-0.8-0.7,0-1.2,0.4-1.9
		C139.8,136.5,140.3,135.1,140.8,133.8z"/>
	<path class="st27" d="M81.7,31.7c4.8,1.9,4.8,1.9,3.2,6.3C82.8,36.5,82.4,34,81.7,31.7z"/>
	<path class="st28" d="M67.3,67.7c-4.3,0.4-4.4,0.4-7.8-2.2C64.5,65.7,64.6,65.7,67.3,67.7z"/>
</g>
<g>
	<g id="g48" transform="translate(38.853,64.1076)">
		<path id="path50" class="st29" d="M14,51.3c-0.7-0.7-1.5-0.7-2.4-0.3c-2.4,1.5-1.9,5.7-0.5,6.9c0.6,0.6,1.4,0.8,2.2,0.5
			c1.3-0.5,2-1.5,2.1-2.9C15.5,53.9,15.8,52.2,14,51.3 M12.7,65.9c0,1,0.2,2.5-0.2,2.7c-0.9,0.5-2.1,0.2-3.2,0.3c-2,0-4-0.6-6-0.1
			c-1.1,0.3-0.6-0.9-0.7-1.5c-0.2-1.2,0.3-2,1.5-2.1c0.4,0,0.7,0.2,1.1,0.3c0-4.7,0-9.3,0-14c0-1-0.4-1.4-1.3-1.3
			c-1.2,0.2-1.1-0.5-1.3-1.3c-0.4-2,0.3-2.3,2.1-2c1.1,0.2,2.3,0,3.4-0.1s2.3-0.3,1.7,1.6c0.8-0.1,1.2-0.6,1.7-0.9
			c2.3-1.6,4.8-0.8,6.7,1.1c2.6,2.6,2,5.8,1.7,8.7c-0.2,2.2-1.9,4-4.3,4.8c-0.8,0.1-1.6,0.2-2.4,0.3c-1.1-0.7-2.4-0.8-3.3-2.1
			c-0.1,1-0.1,1.6-0.2,2.3c-0.4,2.6,0.2,3.2,2.8,2.8l0,0C12.7,65.5,12.7,65.7,12.7,65.9"/>
	</g>
	<g id="g52" transform="translate(67.8021,67.4617)">
		<path id="path54" class="st29" d="M23.7,43.5c2,0,3.9,0,5.8,0c0.9,0,1.4,0.3,0.8,1.4c1.4-0.5,2.5-0.9,3.7-1.3
			c2.6-0.8,4.5,0.6,5,3.3c0.5,2.8,0.1,5.6,0.3,8.4c0.8,0.3,1.8-0.8,2.2,0.2c0.3,0.8,0.4,1.9-0.1,2.6c-0.5,0.7-1.5,0.9-2.4,0.5
			c-0.2-0.1-0.4,0-0.7-0.1c-1.1-0.2-2.6,0.8-3,0c-0.7-1.3-0.6-3-0.7-4.6c-0.1-1.6-0.1-3.2,0-4.9c0-1.1-0.4-1.8-1.5-1.8
			c-1,0-2,0.3-2.3,1.5c-0.2,0.7-0.4,1.5-0.1,2.3c0.2,0.8,0.2,1.8,0.1,2.7c-0.2,1.1-0.6,2.2,1.2,1.3c0.1-0.1,0.4,0,0.5,0.1
			c0.7,1-0.1,2,0.1,3c0.1,0.7-0.6,0.5-1,0.5c-2.2,0.1-4.5-0.6-6.7,0c-1.1,0.3-1-0.7-1.1-1.2c0-0.8-0.7-2.2,0.8-2.3
			c1.4-0.1,1.5-0.8,1.5-1.8c0-2-0.1-3.9-0.1-5.9c0-1.1-0.8-0.8-1.3-0.7c-0.9,0.3-1.2-0.1-0.9-0.7C24,45.3,23.7,44.4,23.7,43.5"/>
	</g>
	<g id="g56" transform="translate(79.1774,43.5658)">
		<path id="path58" class="st29" d="M27.4,99.2c0,0.7,0.2,1.4,0.1,2c-0.2,1.2,0,1.8,1.4,1.4c0.6-0.2,1,0.2,0.9,0.9
			c-0.1,0.9,0.5,2.3-1,2.5c-1.7,0.2-3.4,0.1-5.1,0.1c-0.5,0-0.4-0.4-0.5-0.7c-0.5-2.4-0.5-4.9-0.5-7.3c0-0.2,0.1-0.4,0.1-0.7
			c0-2.1-0.5-2.8-1.9-2.7c-1.6,0.1-2.6,1.5-2.3,3.2c0.2,1,0.2,1.9,0.2,2.9c-0.1,1.1-0.8,2.5,1.4,1.8c0.5-0.1,0.7,0.3,0.6,0.8
			c-0.2,0.7-0.2,1.3-0.2,2c0,0.5-0.2,0.8-0.8,0.8c-2.3,0-4.7-0.3-7,0c-1.4,0.2-0.9-1.1-1.1-1.8c-0.1-0.6-0.3-1.6,0.9-1.6
			c1.2,0.1,1.4-0.5,1.4-1.5c0-2,0.1-3.9-0.1-5.9c-0.1-1-0.2-1.3-1.2-1c-1.1,0.4-0.9,0-1-1c-0.2-2.6,1.1-2.5,3-2.3
			c0.9,0.1,1.8-0.2,2.7-0.2c0.5,0,1.4-0.3,1.2,0.9c-0.1,0.6,0.4,0.2,0.7,0.1c1-0.3,1.9-0.6,2.9-0.9c2.6-0.8,4.6,0.6,4.8,3.2
			c0.1,0.8,0.5,1.6,0.4,2.5C27.4,97.6,27.5,98.4,27.4,99.2C27.5,99.2,27.5,99.2,27.4,99.2"/>
	</g>
	<g id="g60" transform="translate(57.0865,59.8939)">
		<path id="path62" class="st29" d="M20.1,61.1c0,1.5,1,2.5,2.3,2.5c1.1,0,2.7-1.4,2.6-2.4c0-1.8-1.8-1.5-2.7-1.8
			C21.8,59.2,20.2,59.7,20.1,61.1 M15.9,54c2.4-4.2,9.8-4.4,12.9-1c1.2,1.2,0.8,2.9,0.8,4.4s0.2,3-0.1,4.6c-0.2,1,0.6,1,1.4,0.8
			c1.3-0.4,1.1,0.3,1.2,1.3c0.2,2.2-1,1.9-2.4,2c-1.4,0.2-2.8-0.4-4.1,0.2c-0.1,0.1-0.5,0-0.5-0.1c-0.4-2.1-1.3-0.4-2-0.3
			c-2.2,0.6-4.3,0.6-6.1-1.1c-2.1-1.9-1.6-6.2,1-7.3c1.9-0.8,3.9-1.6,6-0.6c0.7,0.3,0.8,0.1,1-0.7c0.3-1.3-0.6-2.1-1.3-2.4
			c-0.9-0.5-2.1-0.4-3.1,0.3C19,55.2,17.5,54.9,15.9,54"/>
	</g>
	<g id="g64" transform="translate(88.1353,47.5919)">
		<path id="path66" class="st29" d="M30.4,89.8c-1.7-0.5-1.9,1.3-2.7,2.3c-0.3,0.4,0.1,0.6,0.6,0.7c1.7,0.5,3.4,0,5.1,0.1
			c0.8,0.1,0.5-0.6,0.4-0.9C33.2,90.6,32.3,89.6,30.4,89.8 M38.2,98.5c-0.7,2-2.4,3.4-4.4,3.5c-1.5,0.1-3.1,0.1-4.6,0.1
			c-2.6,0-5.1-2.4-5.5-5.1c-0.2-1.5-0.9-2.8-0.4-4.4c1.4-4.7,6.3-7.4,11-5.4c2.7,1.2,4.8,5,4.1,7.4c-0.2,0.5-0.3,1.1-0.9,0.9
			c-2.4-0.7-4.8,0.5-7.2-0.2c-0.8-0.2-2.1-0.3-2.3,0.6c-0.2,1.1,0.3,2.3,1.5,2.7c1.3,0.5,2.6,1,3.9,0c1.3-1,2.6-1,4-0.4
			C37.6,98.5,37.9,98.5,38.2,98.5"/>
	</g>
	<g id="g68" transform="translate(89.9971,63.0109)">
		<path id="path70" class="st29" d="M31,53.8c1.4,0.2,1.5-0.2,1.2-1c-0.6-1.2-1.4-2.1-2.9-2.2c-1.7-0.1-2.5,1-3.1,2.3
			c-0.3,0.6,0.1,0.7,0.6,0.8C28.4,54.2,29.9,54,31,53.8 M31,62.9c-0.7,0-1.4,0-2,0.2c-3,0.7-5-1.2-6.4-3.2c-1-1.5-1.3-3.6-1-5.6
			s1.4-3.4,2.9-4.7c2.5-2.3,5.3-2.4,8-1.3c2.5,1,5.1,4.1,4.3,7.4c-0.2,0.8-0.5,0.9-1.3,0.8c-2.7-0.1-5.4,0.2-8.1-0.1
			c-1.3-0.1-1.4,0.8-1.1,1.6c0.6,1.9,3.4,2.9,5.1,1.9c1.3-0.8,2.6-1.4,4.2-0.7c1,0.4,1.1,0.9,0.5,1.6C34.8,62.4,33.1,63.3,31,62.9"
			/>
	</g>
	<g id="g72" transform="translate(46.2325,44.1176)">
		<path id="path74" class="st29" d="M16.5,97.9c-0.5,1.2-0.2,2.5-0.3,3.7c0,0.3,0.2,0.6,0.5,0.6c2.1,0,1.9,1.4,1.9,2.8
			c0,0.6-0.4,0.7-0.9,0.7c-1.6-0.1-3.2,0-4.8-0.3c-0.8-0.2-1.5,0.2-2.3,0.3c-0.7,0.1-1.6,0.2-1-1.1c0.2-0.3,0.2-0.8,0-1.1
			c-0.6-1.2-0.1-1.5,0.9-1.3c1,0.1,1.1-0.8,1-1.2c-0.4-2,0.1-4-0.1-6c-0.1-0.7-0.3-0.9-0.9-0.8c-1,0.1-1.4-0.4-1.2-1.3
			s-0.5-2.4,1.5-2.3c0.8,0.1,0.6-1,0.6-1.5c0.1-1.2,0.3-2.3,1-3.4c1.6-2.4,6.5-2.7,8.7-0.9c0.6,0.5,0.7,1.1,0,1.6
			c-0.6,0.4-1.2,0.9-1.7,1.3c-0.6,0.4-1,0.7-1.3-0.4c-0.3-1-1-0.8-1.5-0.1c-0.5,0.7-0.4,1.5-0.5,2.3c0,1.2,1,0.8,1.6,1
			c1.6,0.5,0.6,1.8,0.8,2.7c0.2,0.9-0.5,0.9-1.1,0.8c-1.2-0.3-1.4,0.2-1.3,1.2C16.3,96.1,15.8,97.1,16.5,97.9"/>
	</g>
	<g id="g76" transform="translate(117.4855,46.4275)">
		<path id="path78" class="st29" d="M40.2,92.5c-1.7,0.1-3.1,0-4.3-1.2c-0.8-0.8-2.3-0.4-3.4-0.2c-0.4,0.1-0.8,0.4-0.7,1
			c0,0.6,0.4,0.7,0.8,0.9c1.6,0.7,3.4,0.4,5,1c3,1.1,4.4,4,3,7c-0.4,0.9-1.4,1.6-1.9,1.7c-1.3,0.3-2.5,0.8-3.9,0.9
			c-1.3,0.1-2-0.8-3.1-1.2c-0.5,1.8-1.7,0.6-2.7,0.7c-0.7,0.1-0.8-0.2-0.8-0.8c0-1.2,0.5-2.4,0.4-3.6c0-0.4,0.5-0.9,0.8-0.6
			c0.7,0.7,1.8-0.9,2.3,0.5c0.7,1.6,3.2,2.5,4.7,1.7c0.7-0.3,1.1-0.9,1-1.6c0-0.6-0.6-0.7-1.2-0.9c-1.4-0.2-2.7-0.8-4.1-1
			c-2.2-0.3-4.4-2.5-4.4-4.2c0-2,2.6-4.4,4.8-4.6c0.9-0.1,2-0.1,2.9,0.2c1.3,0.4,2.4,0.2,3.6-0.1c0.8-0.1,1.5-0.3,1.1,1
			C39.7,90.2,39.8,91.3,40.2,92.5"/>
	</g>
	<g id="g80" transform="translate(100.4151,38.221)">
		<path id="path82" class="st29" d="M34.5,111.7c-0.8-0.4-1.3-1.3-2.4-0.3c-0.6,0.5-1.8-0.3-2.8,0.1c-0.5-1.6,0.2-3.2,0.3-4.7
			c0.1-1.2,1.5,0,2.3-0.6c0.3-0.2,0.9,0,0.8,0.7c0,0.4,0.2,0.7,0.6,0.9c0.2,0.4,0.5,0.3,0.8,0.3l0,0c0.9,0.7,1.9,0.5,2.8,0.4
			c1.2-0.1,1.5-1.1,1-2.3c-0.9-0.3-1.8-0.5-2.7-0.8c-0.8-0.2-1.5-0.3-2.3-0.5c-2.3-0.5-4.4-2.6-4.2-4.5c0.3-2.3,2.3-4,5-4.4
			c0.1,0,0.2,0,0.3,0c2.4,0.4,4.8,0.6,7.2,0.2c-0.4,0.2-0.1,0.6-0.2,0.9c0,0.2-0.1,0.4-0.1,0.7c0,0.3,0,0.6,0,1
			c-0.1,0.6-0.2,1.1,0.3,1.6l-0.1,0.1v0.1c-0.9,0.2-1.8,0.3-2.7,0.1c-1.4-0.9-2.7-1.9-4.5-1.5c-0.7,0.1-1.3,0.3-1.4,0.9
			c-0.1,0.6,0.4,1,1.1,1.3c1.5,0.5,3,0.1,4.5,0.7c0.5,0.2,0.9,0.4,1.4,0.5l0,0c0.2,0.4,0.5,0.5,0.9,0.7c1.6,1,1.9,2.6,1.5,4.4
			c-0.3,1.8-1.3,2.8-3,3.3C37.5,111.4,36.1,112.1,34.5,111.7"/>
	</g>
	<g id="g84" transform="translate(50.6127,72.3858)">
		<path id="path86" class="st29" d="M17.9,32c0,5.7,0.1,11.4-0.1,17c0,1.5,0.5,1.7,1.6,1.4c0.5-0.1,0.6-0.1,0.7,0.3
			c0.5,1.7-0.5,3.3-2.2,3.1c-1.9-0.3-3.8,0-5.7,0c-0.6,0-1.6,0-1.3-0.8c0.4-0.9-1.5-2.6,1-2.6c0.8,0,1.3-0.5,1.3-1.5
			c-0.1-4-0.1-8,0-12.1c0-1-0.3-1.6-1.3-1.6c-2.1-0.1-0.6-1.6-0.9-2.3c-0.3-0.9,0.6-1,1.5-0.9C14.3,32,16.1,32,17.9,32"/>
	</g>
	<g id="g92" transform="translate(59.9906,47.2057)">
		<path id="path94" class="st29" d="M21,90.7c-0.7,0-1.5,0.8-1.9-0.3c-0.4-1.2,0.3-2.8,1.3-3.1c0.3-0.1,1,0.1,0.7-0.6
			c-0.6-1.4,0.5-1.9,1.3-2.6c1-0.8,2.1-1.6,3.3-2.6c0.1,1.1,0,1.9-0.2,2.8c-0.5,2.1,0.2,3,2.4,3.2c0.5,0,0.6,0.2,0.6,0.5
			c-0.2,0.8,0.6,2.2-0.6,2.3c-2.3,0.3-2.1,1.7-2.1,3.3c0,1-0.3,2-0.3,3c0,1.3,0.5,2.4,1.9,2.4c1,0,1.2,0.5,1.1,1.3
			c-0.1,0.7,0.3,1.6-0.9,1.9c-1.3,0.3-2.7,0.4-4,0.2c-1.1-0.2-2-0.8-2.2-2.3C21,97,21.5,93.9,21,90.7"/>
	</g>
	<g id="g96" transform="translate(97.4025,64.7779)">
		<path id="path98" class="st29" d="M33.5,49.7c-0.8-0.5-1.8,0.8-2.1-0.6c-0.4-1.8,0.1-2.8,1.4-3c0.7-0.1,0.8-0.4,0.5-0.9
			c-0.5-1,0-1.4,0.9-1.9c1.3-0.8,2.4-2,3.7-3.1c0.5,1.3,0,2.3-0.2,3.3c-0.4,2,0.3,3,2.3,2.8c0.7-0.1,0.6,0.1,0.6,0.5
			c0,1.2,0.3,2.6-1.6,2.6c-0.6,0-0.8,0.4-0.9,1c-0.1,1.7-0.4,3.5-0.5,5.2c0,1.1,0.6,2.1,1.7,2.2c1.8,0.1,1.2,1.2,1.3,2.1
			c0.1,1.2-1,1.3-1.6,1.3c-0.9-0.1-1.8,0.3-2.6,0.1c-1.2-0.3-2.4-0.5-2.7-2.2c-0.5-2.2-0.2-4.3-0.2-6.4
			C33.5,51.8,32.8,50.7,33.5,49.7"/>
	</g>
	<g id="g100" transform="translate(52.2035,38.3925)">
		<path id="path102" class="st29" d="M18.5,111.3c-0.6,0-1.2,0.2-1.8-0.3c0-0.7-0.1-1.5-0.1-2.2c0-0.5,0-1,0.7-0.8
			c1.8,0.3,1.9-1.1,1.7-2.1c-0.3-1.7,0.1-3.3-0.2-5c-0.2-0.9-0.4-1.3-1.3-1.2c-0.9,0.1-1.2-0.4-0.9-1.1c0.3-0.8-0.3-1.9,0.2-2.4
			c0.6-0.6,1.6,0.2,2.4,0.1c1,0,2,0,2.9-0.2c1.3-0.3,1.4,0.3,1.4,1.3c0,3.1,0,6.1,0,9.2c0,0.7,0,1.2,1,1.3c1.6,0.1,1.1,1.7,1.2,2.7
			c0.1,1.2-0.9,0.7-1.4,0.8C22.3,111.5,20.4,111,18.5,111.3"/>
	</g>
	<g id="g116" transform="translate(56.2092,52.961)">
		<path id="path118" class="st29" d="M19.8,77.3c0,1-1.6,2.8-2.5,2.7c-1,0-2.9-1.8-2.7-2.8c0.1-0.6,0.2-1.3,1.1-1.1
			c0.4,0.1,0.7-0.2,0.9-0.4c0.6-0.5,1-1.3,2-0.6C19,75.5,19.8,76.6,19.8,77.3"/>
	</g>
	<g id="g848" transform="translate(103.4865,46.4585)">
		<path id="path850" class="st30" d="M35.5,92.5c-0.1,0.3-0.2,0.6-0.3,0.9c0,0.2,0,0.3-0.1,0.5c-1.4-0.6-3-0.3-4.5-0.7
			c-0.7-0.2-1.2-0.6-1.1-1.3c0.1-0.6,0.7-0.8,1.4-0.9C32.9,90.6,34.2,91.6,35.5,92.5"/>
	</g>
	<g id="g856" transform="translate(103.1844,45.3855)">
		<path id="path858" class="st0" d="M35.4,95c0-0.2,0-0.3,0.1-0.5c1.3,0.3,1.3,0.3,1.3,1C36.4,95.3,35.9,95.2,35.4,95"/>
	</g>
</g>
</svg>
