# Use Open SSL to generate a certificate

## From the command line:

```
openssl req -config onlinebanking-dev.synovus.com.conf -new -sha256 -newkey rsa:2048 -nodes -keyout onlinebanking-dev.synovus.com.key -x509 -days 365 -out onlinebanking-dev.synovus.com.crt
```

Note: You will need to update your host file (C:\Windows\System32\drivers\etc\hosts) to include the domain being referenced within the certificate (onlinebanking-dev.synovus.com)

# Import the certificate into Chrome

- See: https://stackoverflow.com/a/********/463208

![Test Alt Text](./images/Capturetest.png 'Hello World')
![Test Alt Text](./images/import-certificate-screenshot1.png 'import to Chrome1')

- Restart Chrome Browser for changes to take affect

# Helpful Commands (using Git Bash)

- View certificate
  openssl s_client -proxy proxy.snv.net:8080 -connect onlinebanking-qa.synovus.com:443

- List Alternate DNS Names for a certificate
  openssl s_client -proxy proxy.snv.net:8080 -connect onlinebanking-qa.synovus.com:443 | openssl x509 -noout -text | grep DNS:

- Combine certificates to form the chain
  cat prx-prod.snv.net.cer snv-root-ca.cer > snv-ca-chain.pem

# Notes after failed attempts (probably irrelevant):

- openssl s_client -connect onlinebanking-dev.synovus.com:3081 -servername onlinebanking-dev.synovus.com

- https://medium.com/@superseb/get-your-certificate-chain-right-4b117a9c0fce
