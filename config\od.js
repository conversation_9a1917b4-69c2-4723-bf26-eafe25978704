let UseCases = [];

function CreateUseCase(name, account) {
  return {
    name,
    DepositAccount: account
  };
}

function CreateUseCases() {
  // Use Case 1:  Default to Opt in
  const uc1 = CreateUseCase('DefaultOptIn', {
    IsAllowRegEOptIn: true,
    IsRegulationEOptIn: false
  });

  // Use Case 2:  Default to Opt Out
  const uc2 = CreateUseCase('DefaultOptOut', {
    IsAllowRegEOptIn: true,
    IsRegulationEOptIn: true
  });

  // Use Case 3: No Opt In Allowed
  const uc3 = CreateUseCase('NoOptInAllowedNull', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: null
  });

  // Use Case 4:
  const uc4 = CreateUseCase('NoOptInAllowedAccountType', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Account Type'
  });

  // Use Case 5:
  const uc5 = CreateUseCase('NoOptInAllowedExPlanSuspend', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Exception Plan With Suspend'
  });

  // Use Case 6: button disabled
  const uc6 = CreateUseCase('NoOptInAllowedExPlan', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Exception Plan',
    OverdraftCourtesy: 'Suspended'
  });

  // Use Case 7:
  const uc7 = CreateUseCase('DisplayModalExPlanNull', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Exception Plan',
    OverdraftCourtesy: null
  });

  // Use Case 8:
  const uc8 = CreateUseCase('DisplayModalExPlanBank', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Exception Plan',
    OverdraftCourtesy: 'Bank Opt Out'
  });

  // Use Case 9:
  const uc9 = CreateUseCase('DisplayModalExPlanCustomer', {
    IsAllowRegEOptIn: false,
    OverdraftRestrictionReason: 'Exception Plan',
    OverdraftCourtesy: 'Customer Opt Out'
  });

  const uc10 = CreateUseCase('NotFound', null);

  UseCases = [uc1, uc2, uc3, uc4, uc5, uc6, uc7, uc8, uc9, uc10];
  console.log('Use Cases:', UseCases);
}

function Pop() {
  if (UseCases.length == 0) {
    CreateUseCases();
  }
  const usecase = UseCases.shift();
  console.log('Use Case: ', usecase.name, usecase);
  return usecase;
}

exports.CreateUseCases = CreateUseCase;
exports.Pop = Pop;
