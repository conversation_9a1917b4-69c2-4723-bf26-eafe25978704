<!DOCTYPE html>
<html lang="en">
  <head>
    <title>Down for Maintenance</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">

    <style>
      body {
        background-color: #ffffff;
        color: #333;
        background-image: none;
        font-family: Helvetica, Arial, sans-serif;
        font-size: calc(.875rem);
        letter-spacing: 0.05em;
        padding: 0;
        margin: 0;
      }
      header {
        font-size: calc(1.25rem);
        background-color: #157FA4;
        color: #fff;
        -webkit-box-shadow: none;
        box-shadow: none;
        position: relative;
        height: 110px;
        width: 100%;
      }

      .align-items-center {
        -ms-flex-align: center !important;
        align-items: center !important;
      }

      .justify-content-center {
        -ms-flex-pack: center !important;
        justify-content: center !important;
      }

      .d-flex {
        display: -ms-flexbox !important;
        display: flex !important;
      }

      .text-center {
        text-align: center !important;
      }
    </style>


  </head>

  <body>
    <header class="d-flex justify-content-center align-items-center">
      <svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" x="1000" width="800" height="90" xml:space="preserve">
      <style type="text/css">
        .st3{fill:#fff;}
      </style>
      <g transform="translate(-125,55)">
        <g>
          <path class="st3" d="M149.4,53c9.8-4.3,16.1-13.3,16.1-24.8c0-17-12.9-28.2-30.9-28.2H80.9c8.6,5.9,15.6,13.8,20.3,23.2h27.6
            c6,0,10.6,4.3,10.6,10.4c0,6-4.6,10.6-10.6,10.6h-21.1c0.5,3.5,0.8,7,0.8,10.7c0,3.6-0.3,7-0.8,10.4h24.4
            c6.2,0,10.7,4.4,10.7,10.7c0,6.3-4.6,10.7-10.7,10.7h-31c-4.8,9.4-11.7,17.3-20.3,23.2h58.6c17.8,0,30.8-11.7,30.8-29.2
            C170.2,66.8,161.8,56.5,149.4,53z"/>
        </g>
        <path class="st3" d="M97.7,54.9C97.7,87.1,74.2,110,41,110H0V0h41C74.2,0,97.7,22.9,97.7,54.9z M70.2,54.8
          c0-18.1-12.2-31.1-29.4-31.1h-14v62.7h14C58.1,86.3,70.2,73.2,70.2,54.8z"/>
        <g>
          <path class="st3" d="M226.9,39.1c0,3.7-2.8,6.2-6.6,6.2h-12.7V21.9h11.7c3.8,0,6.6,2.4,6.6,6c0,2.5-1.3,4.4-3.4,5.3
            C225.1,33.9,226.9,36.1,226.9,39.1z M218.1,26.8h-4.7v4.5h4.7c1.3,0,2.3-1,2.3-2.3C220.3,27.7,219.3,26.8,218.1,26.8z M221.1,38.1
            c0-1.3-1-2.3-2.3-2.3h-5.4v4.6h5.4C220.1,40.4,221.1,39.4,221.1,38.1z"/>
          <path class="st3" d="M246.1,41.7h-9.3l-1.2,3.7h-6.2l8.6-23.4h6.9l8.6,23.4h-6.2L246.1,41.7z M244.5,36.9l-3.1-9.5l-3.2,9.5H244.5
            z"/>
          <path class="st3" d="M277.7,21.9v23.4h-7.2L263,29.7v15.6h-5.7V21.9h7.3l7.4,15.6V21.9H277.7z"/>
          <path class="st3" d="M295.3,33.3l8.4,12h-6.6l-7.5-11.2v11.2h-5.7V21.9h5.7v10.6l7.7-10.6h6.6L295.3,33.3z"/>
          <path class="st3" d="M313.6,45.3h-5.7V21.9h5.7V45.3z"/>
          <path class="st3" d="M340.2,21.9v23.4H333l-7.5-15.6v15.6h-5.7V21.9h7.3l7.4,15.6V21.9H340.2z"/>
          <path class="st3" d="M369.7,33.6c0,7-5,12.1-12.2,12.1c-7.2,0-12.3-5.1-12.3-12.1s5.1-12,12.3-12c5.8,0,10.7,3.7,11.7,8.8h-5.9
            c-0.9-2.3-3-3.9-5.7-3.9c-3.8,0-6.5,3-6.5,7.1c0,4.1,2.7,7.1,6.5,7.1c2.7,0,4.9-1.5,5.7-3.7h-5.7v-4.8h12.1
            C369.7,32.5,369.7,33.1,369.7,33.6z"/>
          <path class="st3" d="M224.6,67.3h-6.5v18.4h-5.7V67.3h-6.5v-5h18.7V67.3z"/>
          <path class="st3" d="M234.3,67.3v4.2h10.2v4.8h-10.2v4.3h11v5h-16.7V62.2h16.7v5H234.3z"/>
          <path class="st3" d="M261.9,86c-7.2,0-12.3-5.1-12.3-12.1s5.1-12,12.3-12c6.5,0,11.3,4.1,12.1,10h-5.8c-0.7-2.8-3.1-4.8-6.3-4.8
            c-3.8,0-6.5,2.8-6.5,6.8c0,4,2.7,6.9,6.5,6.9c3.2,0,5.5-2,6.3-4.8h5.8C273.2,81.8,268.3,86,261.9,86z"/>
          <path class="st3" d="M298.1,62.2v23.4h-5.7v-9.2h-7.7v9.2H279V62.2h5.7v9.2h7.7v-9.2H298.1z"/>
          <path class="st3" d="M324.7,62.2v23.4h-7.2L310,70v15.6h-5.7V62.2h7.3l7.4,15.6V62.2H324.7z"/>
          <path class="st3" d="M354.4,73.9c0,7-5.1,12.1-12.3,12.1c-7.2,0-12.3-5.1-12.3-12.1s5.1-12,12.3-12
            C349.2,61.9,354.4,66.9,354.4,73.9z M335.6,73.9c0,4,2.7,6.9,6.5,6.9c3.8,0,6.5-2.9,6.5-6.9c0-3.9-2.7-6.8-6.5-6.8
            C338.3,67.1,335.6,69.9,335.6,73.9z"/>
          <path class="st3" d="M375.2,80.6v5h-15.8V62.2h5.7v18.4H375.2z"/>
          <path class="st3" d="M402.6,73.9c0,7-5.1,12.1-12.3,12.1s-12.3-5.1-12.3-12.1s5.1-12,12.3-12S402.6,66.9,402.6,73.9z M383.8,73.9
            c0,4,2.7,6.9,6.5,6.9c3.8,0,6.5-2.9,6.5-6.9c0-3.9-2.7-6.8-6.5-6.8C386.5,67.1,383.8,69.9,383.8,73.9z"/>
          <path class="st3" d="M430.8,73.9c0,7-5,12.1-12.2,12.1c-7.2,0-12.3-5.1-12.3-12.1s5.1-12,12.3-12c5.8,0,10.7,3.7,11.7,8.8h-5.9
            c-0.9-2.3-3-3.9-5.7-3.9c-3.8,0-6.5,3-6.5,7.1c0,4.1,2.7,7.1,6.5,7.1c2.7,0,4.9-1.5,5.7-3.7h-5.7v-4.8h12.1
            C430.8,72.8,430.8,73.4,430.8,73.9z"/>
          <path class="st3" d="M446.6,76.4v9.2h-5.7v-9.1l-9-14.4h6.5l5.4,9.4l5.4-9.4h6.5L446.6,76.4z"/>
        </g>
      </g>
      </svg>
    </header>

    <div class="d-flex justify-content-center align-items-center" style="margin: 100px 0">
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" x="1000" width="200">
        <style type="text/css">
          .st0{fill:none;stroke:#FFF;stroke-width:2.1369;stroke-miterlimit:10;}
          .st1{fill:#FFF;}
        </style>

        <defs>
          <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:rgb(102, 196, 197);stop-opacity:1" />
            <stop offset="100%" style="stop-color:rgb(0, 156, 159);stop-opacity:1" />
          </linearGradient>
        </defs>

        <circle cx="100" cy="100" r="100" fill="url(#grad1)"/>

        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" x="0" y="0" width="200">
          <!-- reposition the g element below using transform/translate -->
            <g transform="translate(65,55)">
              <g>
                <path class="st0" d="M71.1,79.9c0,4-3.2,7.2-7.2,7.2H8.3c-4,0-7.2-3.2-7.2-7.2V8.3c0-4,3.2-7.2,7.2-7.2h55.6c4,0,7.2,3.2,7.2,7.2
                  V79.9z"/>
              </g>
              <line class="st0" x1="12.1" y1="14.1" x2="60.1" y2="14.1"/>
              <line class="st0" x1="12.1" y1="73.1" x2="60.1" y2="73.1"/>
              <g>
                <path class="st1" d="M21.4,52.9c-0.7-1.6-1.3-3.1-1.9-4.6l0.1-0.1c-0.1,0-0.3,0-0.4,0c-1.2,0-2.4,0-3.6,0c-0.7,0-0.9-0.3-0.9-0.9
                  c0-2.1,0-4.3,0-6.4c0-0.6,0.3-0.9,0.9-0.9c1.3,0,2.7,0,4,0c0.4-1.1,0.8-2.1,1.2-3.1c0.2-0.4,0.4-0.9,0.6-1.3
                  c0.1-0.2,0.1-0.3-0.1-0.5c-0.9-0.9-1.8-1.7-2.6-2.6c-0.5-0.5-0.5-0.9,0-1.3c1.5-1.5,3-3,4.5-4.5c0.5-0.5,0.9-0.5,1.3,0
                  c0.9,0.9,1.9,1.9,2.8,2.8c1.6-0.7,3.1-1.3,4.7-1.9c0-1.2,0-2.5,0-3.8c0-0.8,0.2-1,1-1c2.1,0,4.2,0,6.3,0c0.7,0,1,0.3,1,1
                  c0,1.3,0,2.6,0,3.9c1.6,0.6,3.1,1.3,4.7,1.9c0,0,0.1-0.1,0.2-0.2c0.9-0.9,1.7-1.7,2.6-2.6c0.4-0.4,0.8-0.4,1.3,0
                  c1.5,1.5,3,3,4.6,4.6c0.4,0.4,0.4,0.8,0,1.3c-0.9,0.9-1.9,1.9-2.8,2.8c0.7,1.6,1.3,3.1,1.9,4.7c1.2,0,2.5,0,3.8,0c0.8,0,1,0.2,1,1
                  c0,2.1,0,4.2,0,6.3c0,0.7-0.3,1-1,1c-1.3,0-2.6,0-3.9,0c-0.6,1.6-1.3,3.1-1.9,4.7c0.8,0.8,1.7,1.7,2.7,2.7c0.6,0.6,0.6,0.9,0,1.5
                  c-1.5,1.5-2.9,2.9-4.4,4.4c-0.5,0.5-0.9,0.5-1.4,0c-0.9-0.9-1.9-1.9-2.7-2.7c-1.6,0.7-3.1,1.3-4.7,1.9c0,0.2,0,0.6,0,0.9
                  c0,1,0,2,0,3c0,0.6-0.3,0.9-0.9,0.9c-2.2,0-4.3,0-6.5,0c-0.6,0-0.9-0.3-0.9-0.9c0-1.3,0-2.7,0-3.9c-1.6-0.6-3.1-1.3-4.7-1.9
                  c-0.7,0.7-1.6,1.6-2.4,2.4c-0.1,0.1-0.3,0.3-0.4,0.4c-0.4,0.4-0.8,0.4-1.2,0c-1-1-2.1-2-3.1-3.1c-0.5-0.5-1-1-1.5-1.5
                  c-0.4-0.4-0.4-0.8,0-1.2C19.6,54.7,20.6,53.8,21.4,52.9z M34,63.4c1.4,0,2.8,0,4.2,0c0-0.2,0-0.3,0-0.4c0-1.1,0-2.2,0-3.3
                  c0-0.5,0.2-0.8,0.7-0.9c0.2-0.1,0.5-0.1,0.7-0.2c1.7-0.4,3.4-1.2,4.9-2.1c0.5-0.3,0.7-0.3,1.1,0.1c0.1,0.1,0.2,0.2,0.3,0.3
                  c0.8,0.8,1.6,1.6,2.3,2.3c1-1,2-2,2.9-2.9c-0.8-0.8-1.7-1.7-2.6-2.6c-0.4-0.4-0.4-0.7-0.1-1.2c1.1-1.7,1.8-3.6,2.3-5.5
                  c0.1-0.6,0.3-0.7,0.9-0.7c1.1,0,2.2,0,3.4,0c0.1,0,0.3,0,0.4,0c0-1.4,0-2.8,0-4.2c-0.2,0-0.3,0-0.4,0c-1.1,0-2.2,0-3.3,0
                  c-0.6,0-0.8-0.2-1-0.8c-0.4-2-1.2-3.8-2.3-5.5c-0.3-0.5-0.3-0.8,0.1-1.2c0.8-0.8,1.6-1.6,2.4-2.4C51,32.1,51,32,51.1,32
                  c-1-1-2-2-2.9-2.9c-0.9,0.9-1.7,1.8-2.6,2.6c-0.4,0.4-0.7,0.4-1.1,0.1c-1.7-1.1-3.6-1.9-5.6-2.3c-0.5-0.1-0.7-0.4-0.7-0.9
                  c0-0.7,0-1.4,0-2.1c0-0.6,0-1.1,0-1.7c-1.4,0-2.8,0-4.2,0c0,0.2,0,0.3,0,0.4c0,1.1,0,2.2,0,3.3c0,0.6-0.2,0.8-0.8,1
                  c-2,0.4-3.8,1.2-5.5,2.3c-0.5,0.3-0.8,0.3-1.2-0.1c-0.7-0.7-1.5-1.5-2.2-2.2c-0.1-0.1-0.3-0.3-0.4-0.4c-1,1-2,2-3,3
                  c0.1,0.1,0.2,0.1,0.3,0.2c0.8,0.8,1.6,1.5,2.3,2.3c0.4,0.4,0.5,0.7,0.1,1.2c-1.1,1.7-1.9,3.5-2.3,5.5c-0.1,0.6-0.4,0.8-1,0.8
                  c-1.1,0-2.2,0-3.3,0c-0.1,0-0.3,0-0.4,0c0,1.4,0,2.8,0,4.2c0.2,0,0.3,0,0.4,0c1.1,0,2.2,0,3.3,0c0.6,0,0.8,0.2,0.9,0.8
                  c0,0.2,0.1,0.4,0.1,0.7c0.4,1.7,1.2,3.4,2.1,4.9c0.3,0.5,0.3,0.7-0.1,1.1c-0.1,0.1-0.2,0.2-0.3,0.3c-0.8,0.8-1.6,1.6-2.3,2.3
                  c1,1,2,2,2.9,2.9c0.8-0.8,1.7-1.7,2.5-2.5c0.5-0.5,0.8-0.6,1.4-0.2c1.6,1.1,3.4,1.8,5.3,2.2c0.7,0.2,0.9,0.3,0.9,1.1
                  C34,61,34,62.2,34,63.4z"/>
                <path class="st1" d="M36.3,53.7c-4,0-8.2-2.6-9.5-6.8c-0.5-1.7-0.5-3.4-0.1-5c0.7-2.6,2.1-4.7,4.5-6c1.3-0.8,2.8-1.3,4.4-1.4
                  c4-0.3,8.9,2.7,9.9,7.4c0.5,2.2,0.1,4.2-0.8,6.2c-1.2,2.5-3,4.3-5.6,5.2c-0.9,0.3-1.8,0.4-2.8,0.6C36.3,53.8,36.3,53.8,36.3,53.7z
                   M43.7,44.3c0-3.6-2.7-7.7-7-7.9c-4.4-0.2-8.2,3.2-8.2,7.2c0,4.5,3.3,7.8,7.5,7.9C39.8,51.7,43.8,48.4,43.7,44.3z"/>
              </g>
            </g>
        </svg>
      </svg>
    </div>

    <div class="text-center">
      <h2>D3 Banking Technology is undergoing maintenance.</h2>
      <h2>Please check back again soon.</h2>
    </div>

  </body>
</html>