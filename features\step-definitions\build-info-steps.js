const { Given, When, Then, setDefaultTimeout } = require('cucumber');
const { expect } = require('chai');
const PuppeteerPage = require('../pages/page');
const { getApiUrl } = require('../support/urls');

setDefaultTimeout(60 * 1000);

Given('I want to check the app version', function () {
  this.page = new PuppeteerPage(this.browser);
});

When('I view the info endpoint', async function () {
  return await this.page.visit(`${getApiUrl()}/info`);
});

Then('I can see the app version', function () {
  return 'pending';
});
