<?xml version="1.0" encoding="UTF-8"?>
<svg width="20px" height="20px" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Icons/Retail/in</title>
    <defs>
        <circle id="path-1" cx="8" cy="8" r="8"></circle>
        <filter x="-56.2%" y="-56.2%" width="212.5%" height="212.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="0" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="3" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.18 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="Icons/Retail/in" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Group-3-Copy-2" transform="translate(2.000000, 2.000000)">
            <g id="Ellipse_72">
                <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                <use fill="#616266" fill-rule="evenodd" xlink:href="#path-1"></use>
            </g>
            <g id="Group-2" transform="translate(8.000000, 8.500000) scale(1, -1) translate(-8.000000, -8.500000) translate(3.000000, 5.000000)" stroke="#15A4FF" stroke-width="2">
                <polyline id="Path-3" points="0 4 5 0 10 4"></polyline>
                <line x1="5" y1="1" x2="5" y2="7" id="Line" stroke-linecap="square"></line>
            </g>
        </g>
    </g>
</svg>