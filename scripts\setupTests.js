/* eslint no-console: off */
/* eslint-env jest */
import '@d3banking/testing-library';
import '@testing-library/jest-dom';
import * as Yup from 'yup';
import axios from 'axios';

const moment = require('moment');

global.d3 = {
  getCompanyAttribute: jest.fn(),
  lib: {
    moment,
    axios,
    i18next: { t: t => t, exists: () => true }
  },
  store: {
    getState: () => ({
      accounts: {
        accounts: undefined,
        attributes: [],
        details: {},
        links: {}
      },
      systemSettings: {}
    })
  }
};

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // Deprecated
    removeListener: jest.fn(), // Deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn()
  }))
});

jest.mock('@d3banking/l10n/lib/hooks', () => ({
  ...jest.requireActual('@d3banking/l10n/lib/hooks'),
  useL10nBundle: jest.fn(() => true)
}));

jest.mock('@d3banking/l10n/lib/util/t', () => ({
  t: key => key
}));

Yup.addMethod(Yup.string, 'equalTo', function a() {
  return this.test(
    'equalTo',
    () => '',
    () => true
  );
});
Yup.addMethod(Yup.string, 'passwordPattern', function b() {
  return this.test(
    'passwordPattern',
    () => '',
    () => true
  );
});
Yup.addMethod(Yup.date, 'dateFieldPattern', function c() {
  return this.test(
    'dateFieldPattern',
    () => '',
    () => true
  );
});
Yup.addMethod(Yup.string, 'textFieldPattern', function d() {
  return this.test(
    'textFieldPattern',
    () => '',
    () => true
  );
});
Yup.addMethod(Yup.string, 'usernamePattern', function e() {
  return this.test(
    'usernamePattern',
    () => '',
    () => true
  );
});
