import { eventService } from '@d3banking/event-service';
import { t } from '@d3banking/l10n';

import * as D3 from '../common/extension';
import { addInjectClassName, loadL10nBundle } from '../common/d3';

function addButton() {
  const addAccountButton = document.createElement('Button');
  addAccountButton.id = 'AddExternalAccountBtn';
  addAccountButton.innerText = t('ui-synovus:ext.add-an-account.button', 'Add An Account');
  addAccountButton.style.margin = '1rem 1rem 2rem 1rem';

  addInjectClassName(addAccountButton);

  const acc = document.querySelector('.other-account-groups');
  if (acc) {
    acc.insertAdjacentElement('afterend', addAccountButton);
  }

  // Grab the "click" event from the account-options-button and use it s the action for the onclick event of your new button.

  addAccountButton.onclick = () => {
    eventService.dispatchEvent('openDrawer:addAccountDrawer');
    console.warn("Click event for 'Add an account' could not be found.");
  };
}

const extension: D3.Extension = {
  uiVersion: '6.2',
  eventName: 'AddAccount',
  callOncePerPage: true,

  isTargetPage: href => {
    const exp = /^accounts\/overview$/i;
    return exp.test(href);
  },

  // The Mutation Observer Event used to filter and detect when the keyElement element has been mutated.
  filter: () => {
    const isAccOptionsButtonExists = !!document.querySelector('.other-account-groups');

    const isNewButtonAdded = !!document.querySelector('#AddExternalAccountBtn.ext-visited');

    return isAccOptionsButtonExists && !isNewButtonAdded;
  },

  eventCallback: () => addButton()
};

loadL10nBundle().then(() => {
  D3.init(extension);
});
