const { i10GeneralExport } = require('../i10-general-export');

const bundle = 'ui-synovus';

const personeticsL10nDefinition = {
  'personetics.widget.title': {
    value: 'Insights',
    description: 'The title of the Personetics widget',
    version: '4.16'
  },
  'personetics.widget.button': {
    value: 'View All Insights',
    description: 'View All Insights button text on overview widget',
    version: '4.16'
  },
  'personetics.nav.insights': {
    value: 'Insights',
    description: 'Top navigation text',
    version: '4.16'
  },
  'personetics.page.title': {
    value: 'My Synovus Insights',
    description: 'Insights page title',
    version: '4.16'
  },

  /**
  * DPD-42987: Temporary workaround to connect locally defined custom l10ns from mobile application with Control UI.
  * Defining l10ns here will include them into the publishing process.
  * Once the proper sync is established, definitions will be moved.
  */
  'personetics.insights.label': {
    value: 'My Synovus Insights',
    description: 'The label for the View Insights button.',
    version: '5.3'
  },
  'personetics.insightsButton.withBadge.accessibilityLabel': {
    value: 'View Insights, you have new insights',
    description: 'The accessibility label for the Insights dot when you have new Insights.',
    version: '5.3'
  },
  'personetics.insightsButton.withoutBadge.accessibilityLabel': {
    value: 'View Insights',
    description: 'The accessibility label for the Insights dot when you do not have new Insights.',
    version: '5.3'
  }
};

module.exports = i10GeneralExport(personeticsL10nDefinition, bundle);
