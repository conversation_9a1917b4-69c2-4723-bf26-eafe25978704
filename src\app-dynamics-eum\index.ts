import * as D3 from '../common/extension';

/*
// EXAMPLE :  INJECT THIS CODE INTO THE PAGE
        <script charset='UTF-8'>
    window['adrum-start-time'] = new Date().getTime();
    (function(config){
        config.appKey = '<EUM_APP_KEY>';
        config.adrumExtUrlHttp = 'http://cdn.appdynamics.com';
        config.adrumExtUrlHttps = 'https://cdn.appdynamics.com';
        config.beaconUrlHttp = 'http://col.eum-appdynamics.com';
        config.beaconUrlHttps = 'https://col.eum-appdynamics.com';
        config.xd = {enable : false};
        config.spa = {
            "spa2": true
        };
    })(window['adrum-config'] || (window['adrum-config'] = {}));
    </script>
    <script src='//cdn.appdynamics.com/adrum/adrum-latest.js' type='text/javascript' charset='UTF-8'></script>
    <script src="//cdn.appdynamics.com/adrum/adrum-20.8.0.3230.js"></script>
}
*/

let uid; // Sets the UID for the EUM :-)

const link = 'https://cdn.appdynamics.com/adrum/adrum-latest.js';

const addInlineScript = (code: string) => {
  const newScript = document.createElement('script');
  const inlineScript = document.createTextNode(code);
  newScript.appendChild(inlineScript);
  const headTag = document.querySelector('head');
  if (headTag) {
    headTag.appendChild(newScript);
  }
};

const addExternalScript = (alink: string) => {
  const newScript = document.createElement('script');
  newScript.src = alink;
  newScript.id = 'app-dynamics-eum';
  newScript.type = 'text/javascript';
  const headTag = document.querySelector('head');
  if (headTag) {
    headTag.appendChild(newScript);
  }
};

const extension: D3.Extension = {
  uiVersion: '6.2',
  eventName: 'app-dynamics-eum',
  callOncePerPage: true, // This ensures that once filter evaluates to true, it stops watching for updates.

  isTargetPage: () => {
    return true;
  },

  filter: () => {
    const headTag = document.querySelector('head');

    const scriptTag = document.querySelector('script#app-dynamics-eum');

    return !!headTag && !scriptTag;
  },

  eventCallback: () => {
    uid = d3.store.getState().systemSettings['ext.app-dynamics-eum.appkey'] || 'AD-AAB-AAZ-GRS';
    const code = `
            window["adrum-start-time"] = new Date().getTime();
            (function(config){
                config.appKey = "${uid}";
                config.adrumExtUrlHttp = "http://cdn.appdynamics.com";
                config.adrumExtUrlHttps = "https://cdn.appdynamics.com";
                config.beaconUrlHttp = "http://pdx-col.eum-appdynamics.com";
                config.beaconUrlHttps = "https://pdx-col.eum-appdynamics.com";
                config.useHTTPSAlways = true;
                config.xd = {"enable": true};
                config.resTiming = {"bufSize":200,"clearResTimingOnBeaconSend":true};
                config.maxUrlLength = 512;
                config.spa = {"spa2":true};
            })(window["adrum-config"] || (window["adrum-config"] = {}));
            `;
    addInlineScript(code);
    addExternalScript(link);
  }
};

void D3.init(extension);
