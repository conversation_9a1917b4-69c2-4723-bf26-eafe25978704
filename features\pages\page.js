class PuppeteerPage {
  constructor(browser) {
    this.browser = browser;
  }

  async visit(url) {
    this.page = await this.browser.newPage();
    await this.page.goto(url, {
      waitUntil: 'networkidle2',
      timeout: 60000
    });
    return this.page;
  }

  async close() {
    await this.browser.close();
  }

  async getElementText(selector) {
    return await this.page.$eval(selector, element => element.textContent);
  }
}

module.exports = PuppeteerPage;
